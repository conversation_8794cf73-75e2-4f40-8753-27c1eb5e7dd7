!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).zrender={})}(this,function(t){"use strict";var e=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},c=new function(){this.browser=new e,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(c.wxa=!0,c.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?c.worker=!0:"undefined"==typeof navigator?(c.node=!0,c.svgSupported=!0):function(t,e){var r=e.browser,i=t.match(/Firefox\/([\d.]+)/),n=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(r.firefox=!0,r.version=i[1]);n&&(r.ie=!0,r.version=n[1]);o&&(r.edge=!0,r.version=o[1],r.newEdge=18<+o[1].split(".")[0]);a&&(r.weChat=!0);e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,e.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&11<=+r.version),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||r.ie&&9<=+r.version}(navigator.userAgent,c);var h=12,g="sans-serif",E=h+"px "+g;var l,u,p=function(t){var e={};if("undefined"==typeof JSON)return e;for(var r=0;r<t.length;r++){var i=String.fromCharCode(r+32),n=(t.charCodeAt(r)-20)/100;e[i]=n}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),f={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){var r;if(l||(r=f.createCanvas(),l=r&&r.getContext("2d")),l)return u!==e&&(u=l.font=e||E),l.measureText(t);t=t||"";var i=/(\d+)px/.exec(e=e||E),n=i&&+i[1]||h,o=0;if(0<=e.indexOf("mono"))o=n*t.length;else for(var a=0;a<t.length;a++){var s=p[t[a]];o+=null==s?n:s*n}return{width:o}},loadImage:function(t,e,r){var i=new Image;return i.onload=e,i.onerror=r,i.src=t,i}};var s=D(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),d=D(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),y=Object.prototype.toString,r=Array.prototype,a=r.forEach,v=r.filter,n=r.slice,_=r.map,i=function(){}.constructor,o=i?i.prototype:null,m="__proto__",x=2311;function w(){return x++}function b(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function C(t){if(null==t||"object"!=typeof t)return t;var e=t,r=y.call(t);if("[object Array]"===r){if(!rt(t)){e=[];for(var i=0,n=t.length;i<n;i++)e[i]=C(t[i])}}else if(d[r]){if(!rt(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,n=t.length;i<n;i++)e[i]=t[i]}}}else if(!s[r]&&!rt(t)&&!Y(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==m&&(e[a]=C(t[a]));return e}function S(t,e,r){if(!W(e)||!W(t))return r?C(e):t;for(var i in e){var n,o;e.hasOwnProperty(i)&&i!==m&&(n=t[i],!W(o=e[i])||!W(n)||G(o)||G(n)||Y(o)||Y(n)||X(o)||X(n)||rt(o)||rt(n)?!r&&i in t||(t[i]=C(e[i])):S(n,o,r))}return t}function I(t,e){if(Object.assign)Object.assign(t,e);else for(var r in e)e.hasOwnProperty(r)&&r!==m&&(t[r]=e[r]);return t}function k(t,e,r){for(var i=F(e),n=0;n<i.length;n++){var o=i[n];(r?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var T=f.createCanvas;function P(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r}return-1}function M(t,e,r){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),n=0;n<i.length;n++){var o=i[n];"constructor"!==o&&(r?null!=e[o]:null==t[o])&&(t[o]=e[o])}else k(t,e,r)}function A(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function O(t,e,r){if(t&&e)if(t.forEach&&t.forEach===a)t.forEach(e,r);else if(t.length===+t.length)for(var i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(r,t[o],o,t)}function L(t,e,r){if(!t)return[];if(!e)return Q(t);if(t.map&&t.map===_)return t.map(e,r);for(var i=[],n=0,o=t.length;n<o;n++)i.push(e.call(r,t[n],n,t));return i}function D(t,e,r,i){if(t&&e){for(var n=0,o=t.length;n<o;n++)r=e.call(i,r,t[n],n,t);return r}}function R(t,e,r){if(!t)return[];if(!e)return Q(t);if(t.filter&&t.filter===v)return t.filter(e,r);for(var i=[],n=0,o=t.length;n<o;n++)e.call(r,t[n],n,t)&&i.push(t[n]);return i}function F(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var r in t)t.hasOwnProperty(r)&&e.push(r);return e}var z=o&&B(o.bind)?o.call.bind(o.bind):function(t,e){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return t.apply(e,r.concat(n.call(arguments)))}};function G(t){return Array.isArray?Array.isArray(t):"[object Array]"===y.call(t)}function B(t){return"function"==typeof t}function H(t){return"string"==typeof t}function N(t){return"number"==typeof t}function W(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function X(t){return!!s[y.call(t)]}function q(t){return!!d[y.call(t)]}function Y(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function j(t){return null!=t.colorStops}function V(t){return null!=t.image}function U(t){return t!=t}function Z(t,e){return null!=t?t:e}function K(t,e,r){return null!=t?t:null!=e?e:r}function Q(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return n.apply(t,e)}function $(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function J(t,e){if(!t)throw new Error(e)}function tt(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var et="__ec_primitive__";function rt(t){return t[et]}var it=(nt.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},nt.prototype.has=function(t){return this.data.hasOwnProperty(t)},nt.prototype.get=function(t){return this.data[t]},nt.prototype.set=function(t,e){return this.data[t]=e,this},nt.prototype.keys=function(){return F(this.data)},nt.prototype.forEach=function(t){var e=this.data;for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)},nt);function nt(){this.data={}}var ot="function"==typeof Map;var at=(st.prototype.hasKey=function(t){return this.data.has(t)},st.prototype.get=function(t){return this.data.get(t)},st.prototype.set=function(t,e){return this.data.set(t,e),e},st.prototype.each=function(r,i){this.data.forEach(function(t,e){r.call(i,t,e)})},st.prototype.keys=function(){var t=this.data.keys();return ot?Array.from(t):t},st.prototype.removeKey=function(t){this.data.delete(t)},st);function st(t){var r=G(t);this.data=new(ot?Map:it);var i=this;function e(t,e){r?i.set(t,e):i.set(e,t)}t instanceof st?t.each(e):t&&O(t,e)}function ht(t,e){var r,i=Object.create?Object.create(t):((r=function(){}).prototype=t,new r);return e&&I(i,e),i}function lt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function ut(t,e){return t.hasOwnProperty(e)}function ct(){}var pt=180/Math.PI,ft=Object.freeze({__proto__:null,guid:w,logError:b,clone:C,merge:S,mergeAll:function(t,e){for(var r=t[0],i=1,n=t.length;i<n;i++)r=S(r,t[i],e);return r},extend:I,defaults:k,createCanvas:T,indexOf:P,inherits:function(t,e){var r=t.prototype;function i(){}for(var n in i.prototype=e.prototype,t.prototype=new i,r)r.hasOwnProperty(n)&&(t.prototype[n]=r[n]);(t.prototype.constructor=t).superClass=e},mixin:M,isArrayLike:A,each:O,map:L,reduce:D,filter:R,find:function(t,e,r){if(t&&e)for(var i=0,n=t.length;i<n;i++)if(e.call(r,t[i],i,t))return t[i]},keys:F,bind:z,curry:function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return function(){return t.apply(this,e.concat(n.call(arguments)))}},isArray:G,isFunction:B,isString:H,isStringSafe:function(t){return"[object String]"===y.call(t)},isNumber:N,isObject:W,isBuiltInObject:X,isTypedArray:q,isDom:Y,isGradientObject:j,isImagePatternObject:V,isRegExp:function(t){return"[object RegExp]"===y.call(t)},eqNaN:U,retrieve:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0,i=t.length;r<i;r++)if(null!=t[r])return t[r]},retrieve2:Z,retrieve3:K,slice:Q,normalizeCssArray:$,assert:J,trim:tt,setAsPrimitive:function(t){t[et]=!0},isPrimitive:rt,HashMap:at,createHashMap:function(t){return new at(t)},concatArray:function(t,e){for(var r=new t.constructor(t.length+e.length),i=0;i<t.length;i++)r[i]=t[i];for(var n=t.length,i=0;i<e.length;i++)r[i+n]=e[i];return r},createObject:ht,disableUserSelect:lt,hasOwn:ut,noop:ct,RADIAN_TO_DEGREE:pt}),dt=function(t,e){return(dt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function yt(t,e){function r(){this.constructor=t}dt(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function gt(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function vt(t){return[t[0],t[1]]}function _t(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function mt(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function xt(t){return Math.sqrt(wt(t))}function wt(t){return t[0]*t[0]+t[1]*t[1]}function bt(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function St(t,e){var r=xt(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t}function kt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Tt=kt;function Ct(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Pt=Ct;function Mt(t,e,r,i){return t[0]=e[0]+i*(r[0]-e[0]),t[1]=e[1]+i*(r[1]-e[1]),t}function At(t,e,r){var i=e[0],n=e[1];return t[0]=r[0]*i+r[2]*n+r[4],t[1]=r[1]*i+r[3]*n+r[5],t}function Lt(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function Dt(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}var zt=Object.freeze({__proto__:null,create:gt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:vt,set:function(t,e,r){return t[0]=e,t[1]=r,t},add:_t,scaleAndAdd:function(t,e,r,i){return t[0]=e[0]+r[0]*i,t[1]=e[1]+r[1]*i,t},sub:mt,len:xt,length:xt,lenSquare:wt,lengthSquare:wt,mul:function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t},div:function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:bt,normalize:St,distance:kt,dist:Tt,distanceSquare:Ct,distSquare:Pt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:Mt,applyTransform:At,min:Lt,max:Dt}),It=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},Ot=(Rt.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new It(e,t),"dragstart",t.event))},Rt.prototype._drag=function(t){var e,r,i,n,o,a,s=this._draggingTarget;s&&(e=t.offsetX,r=t.offsetY,i=e-this._x,n=r-this._y,this._x=e,this._y=r,s.drift(i,n,t),this.handler.dispatchToElement(new It(s,t),"drag",t.event),o=this.handler.findHover(e,r,s).target,a=this._dropTarget,s!==(this._dropTarget=o)&&(a&&o!==a&&this.handler.dispatchToElement(new It(a,t),"dragleave",t.event),o&&o!==a&&this.handler.dispatchToElement(new It(o,t),"dragenter",t.event)))},Rt.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new It(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new It(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},Rt);function Rt(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}var Ft=(Bt.prototype.on=function(t,e,r,i){this._$handlers||(this._$handlers={});var n=this._$handlers;if("function"==typeof e&&(i=r,r=e,e=null),!r||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),n[t]||(n[t]=[]);for(var a=0;a<n[t].length;a++)if(n[t][a].h===r)return this;var s={h:r,query:e,ctx:i||this,callAtLast:r.zrEventfulCallAtLast},h=n[t].length-1,l=n[t][h];return l&&l.callAtLast?n[t].splice(h,0,s):n[t].push(s),this},Bt.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},Bt.prototype.off=function(t,e){var r=this._$handlers;if(!r)return this;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var i=[],n=0,o=r[t].length;n<o;n++)r[t][n].h!==e&&i.push(r[t][n]);r[t]=i}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},Bt.prototype.trigger=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var h=i[s];if(!n||!n.filter||null==h.query||n.filter(t,h.query))switch(o){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,e[0]);break;case 2:h.h.call(h.ctx,e[0],e[1]);break;default:h.h.apply(h.ctx,e)}}return n&&n.afterTrigger&&n.afterTrigger(t),this},Bt.prototype.triggerWithContext=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,h=0;h<s;h++){var l=i[h];if(!n||!n.filter||null==l.query||n.filter(t,l.query))switch(o){case 0:l.h.call(a);break;case 1:l.h.call(a,e[0]);break;case 2:l.h.call(a,e[0],e[1]);break;default:l.h.apply(a,e.slice(1,o-1))}}return n&&n.afterTrigger&&n.afterTrigger(t),this},Bt);function Bt(t){t&&(this._$eventProcessor=t)}var Ht=Math.log(2);function Nt(t,e,r,i,n,o){var a=i+"-"+n,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var h=Math.round(Math.log((1<<s)-1&~n)/Ht);return t[r][h]}for(var l=i|1<<r,u=r+1;i&1<<u;)u++;for(var c=0,p=0,f=0;p<s;p++){var d=1<<p;d&n||(c+=(f%2?-1:1)*t[r][p]*Nt(t,e-1,u,l,n|d,o),f++)}return o[a]=c}function Et(t,e){var r=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},n=Nt(r,8,0,0,0,i);if(0!==n){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Nt(r,7,0===a?1:0,1<<a,1<<s,i)/n*e[a];return function(t,e,r){var i=e*o[6]+r*o[7]+1;t[0]=(e*o[0]+r*o[1]+o[2])/i,t[1]=(e*o[3]+r*o[4]+o[5])/i}}}var Wt="___zrEVENTSAVED";function Xt(t,e,r,i,n){if(e.getBoundingClientRect&&c.domSupported&&!qt(e)){var o=e[Wt]||(e[Wt]={}),a=function(t,e,r){for(var i=r?"invTrans":"trans",n=e[i],o=e.srcCoords,a=[],s=[],h=!0,l=0;l<4;l++){var u=t[l].getBoundingClientRect(),c=2*l,p=u.left,f=u.top;a.push(p,f),h=h&&o&&p===o[c]&&f===o[1+c],s.push(t[l].offsetLeft,t[l].offsetTop)}return h&&n?n:(e.srcCoords=a,e[i]=r?Et(s,a):Et(a,s))}(function(t,e){var r=e.markers;if(r)return r;r=e.markers=[];for(var i=["left","right"],n=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,h=o%2,l=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[h]+":0",n[l]+":0",i[1-h]+":auto",n[1-l]+":auto",""].join("!important;"),t.appendChild(a),r.push(a)}return r}(e,o),o,n);if(a)return a(t,r,i),1}}function qt(t){return"CANVAS"===t.nodeName.toUpperCase()}var Yt=/([&<>"'])/g,jt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};var Vt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ut=[],Gt=c.browser.firefox&&+c.browser.version.split(".")[0]<39;function Zt(t,e,r,i){return r=r||{},i?Kt(t,e,r):Gt&&null!=e.layerX&&e.layerX!==e.offsetX?(r.zrX=e.layerX,r.zrY=e.layerY):null!=e.offsetX?(r.zrX=e.offsetX,r.zrY=e.offsetY):Kt(t,e,r),r}function Kt(t,e,r){if(c.domSupported&&t.getBoundingClientRect){var i=e.clientX,n=e.clientY;if(qt(t)){var o=t.getBoundingClientRect();return r.zrX=i-o.left,void(r.zrY=n-o.top)}if(Xt(Ut,t,i,n))return r.zrX=Ut[0],void(r.zrY=Ut[1])}r.zrX=r.zrY=0}function Qt(t){return t||window.event}function $t(t,e,r){if(null!=(e=Qt(e)).zrX)return e;var i,n,o=e.type;o&&0<=o.indexOf("touch")?(i="touchend"!==o?e.targetTouches[0]:e.changedTouches[0])&&Zt(t,i,e,r):(Zt(t,e,e,r),n=function(t){var e=t.wheelDelta;if(e)return e;var r=t.deltaX,i=t.deltaY;return null!=r&&null!=i?3*(0!==i?Math.abs(i):Math.abs(r))*(0<i||!(i<0)&&0<r?-1:1):e}(e),e.zrDelta=n?n/120:-(e.detail||0)/3);var a=e.button;return null==e.which&&void 0!==a&&Vt.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}var Jt=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},te=(ee.prototype.recognize=function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},ee.prototype.clear=function(){return this._track.length=0,this},ee.prototype._doTrack=function(t,e,r){var i=t.touches;if(i){for(var n={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],h=Zt(r,s,{});n.points.push([h.zrX,h.zrY]),n.touches.push(s)}this._track.push(n)}},ee.prototype._recognize=function(t){for(var e in ie)if(ie.hasOwnProperty(e)){var r=ie[e](this._track,t);if(r)return r}},ee);function ee(){this._track=[]}function re(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}var ie={pinch:function(t,e){var r=t.length;if(r){var i,n=(t[r-1]||{}).points,o=(t[r-2]||{}).points||n;if(o&&1<o.length&&n&&1<n.length){var a=re(n)/re(o);isFinite(a)||(a=1),e.pinchScale=a;var s=[((i=n)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function ne(){return[1,0,0,1,0,0]}function oe(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ae(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function se(t,e,r){var i=e[0]*r[0]+e[2]*r[1],n=e[1]*r[0]+e[3]*r[1],o=e[0]*r[2]+e[2]*r[3],a=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],h=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=i,t[1]=n,t[2]=o,t[3]=a,t[4]=s,t[5]=h,t}function he(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t}function le(t,e,r){var i=e[0],n=e[2],o=e[4],a=e[1],s=e[3],h=e[5],l=Math.sin(r),u=Math.cos(r);return t[0]=i*u+a*l,t[1]=-i*l+a*u,t[2]=n*u+s*l,t[3]=-n*l+u*s,t[4]=u*o+l*h,t[5]=u*h-l*o,t}function ue(t,e,r){var i=r[0],n=r[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function ce(t,e){var r=e[0],i=e[2],n=e[4],o=e[1],a=e[3],s=e[5],h=r*a-o*i;return h?(h=1/h,t[0]=a*h,t[1]=-o*h,t[2]=-i*h,t[3]=r*h,t[4]=(i*s-a*n)*h,t[5]=(o*n-r*s)*h,t):null}var pe=Object.freeze({__proto__:null,create:ne,identity:oe,copy:ae,mul:se,translate:he,rotate:le,scale:ue,invert:ce,clone:function(t){var e=ne();return ae(e,t),e}}),fe=(de.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},de.prototype.clone=function(){return new de(this.x,this.y)},de.prototype.set=function(t,e){return this.x=t,this.y=e,this},de.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},de.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},de.prototype.scale=function(t){this.x*=t,this.y*=t},de.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},de.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},de.prototype.dot=function(t){return this.x*t.x+this.y*t.y},de.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},de.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},de.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},de.prototype.distance=function(t){var e=this.x-t.x,r=this.y-t.y;return Math.sqrt(e*e+r*r)},de.prototype.distanceSquare=function(t){var e=this.x-t.x,r=this.y-t.y;return e*e+r*r},de.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},de.prototype.transform=function(t){if(t){var e=this.x,r=this.y;return this.x=t[0]*e+t[2]*r+t[4],this.y=t[1]*e+t[3]*r+t[5],this}},de.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},de.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},de.set=function(t,e,r){t.x=e,t.y=r},de.copy=function(t,e){t.x=e.x,t.y=e.y},de.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},de.lenSquare=function(t){return t.x*t.x+t.y*t.y},de.dot=function(t,e){return t.x*e.x+t.y*e.y},de.add=function(t,e,r){t.x=e.x+r.x,t.y=e.y+r.y},de.sub=function(t,e,r){t.x=e.x-r.x,t.y=e.y-r.y},de.scale=function(t,e,r){t.x=e.x*r,t.y=e.y*r},de.scaleAndAdd=function(t,e,r,i){t.x=e.x+r.x*i,t.y=e.y+r.y*i},de.lerp=function(t,e,r,i){var n=1-i;t.x=n*e.x+i*r.x,t.y=n*e.y+i*r.y},de);function de(t,e){this.x=t||0,this.y=e||0}var ye=Math.min,ge=Math.max,ve=new fe,_e=new fe,me=new fe,xe=new fe,we=new fe,be=new fe,Se=(ke.prototype.union=function(t){var e=ye(t.x,this.x),r=ye(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=ge(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=ge(t.y+t.height,this.y+this.height)-r:this.height=t.height,this.x=e,this.y=r},ke.prototype.applyTransform=function(t){ke.applyTransform(this,this,t)},ke.prototype.calculateTransform=function(t){var e=t.width/this.width,r=t.height/this.height,i=ne();return he(i,i,[-this.x,-this.y]),ue(i,i,[e,r]),he(i,i,[t.x,t.y]),i},ke.prototype.intersect=function(t,e){if(!t)return!1;t instanceof ke||(t=ke.create(t));var r,i,n,o,a,s,h,l,u=this,c=u.x,p=u.x+u.width,f=u.y,d=u.y+u.height,y=t.x,g=t.x+t.width,v=t.y,_=t.y+t.height,m=!(p<y||g<c||d<v||_<f);return e&&(r=1/0,i=0,n=Math.abs(p-y),o=Math.abs(g-c),a=Math.abs(d-v),s=Math.abs(_-f),h=Math.min(n,o),l=Math.min(a,s),p<y||g<c?i<h&&(i=h,n<o?fe.set(be,-n,0):fe.set(be,o,0)):h<r&&(r=h,n<o?fe.set(we,n,0):fe.set(we,-o,0)),d<v||_<f?i<l&&(i=l,a<s?fe.set(be,0,-a):fe.set(be,0,s)):h<r&&(r=h,a<s?fe.set(we,0,a):fe.set(we,0,-s))),e&&fe.copy(e,m?we:be),m},ke.prototype.contain=function(t,e){var r=this;return t>=r.x&&t<=r.x+r.width&&e>=r.y&&e<=r.y+r.height},ke.prototype.clone=function(){return new ke(this.x,this.y,this.width,this.height)},ke.prototype.copy=function(t){ke.copy(this,t)},ke.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},ke.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},ke.prototype.isZero=function(){return 0===this.width||0===this.height},ke.create=function(t){return new ke(t.x,t.y,t.width,t.height)},ke.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},ke.applyTransform=function(t,e,r){if(r){if(r[1]<1e-5&&-1e-5<r[1]&&r[2]<1e-5&&-1e-5<r[2]){var i=r[0],n=r[3],o=r[4],a=r[5];return t.x=e.x*i+o,t.y=e.y*n+a,t.width=e.width*i,t.height=e.height*n,t.width<0&&(t.x+=t.width,t.width=-t.width),void(t.height<0&&(t.y+=t.height,t.height=-t.height))}ve.x=me.x=e.x,ve.y=xe.y=e.y,_e.x=xe.x=e.x+e.width,_e.y=me.y=e.y+e.height,ve.transform(r),xe.transform(r),_e.transform(r),me.transform(r),t.x=ye(ve.x,_e.x,me.x,xe.x),t.y=ye(ve.y,_e.y,me.y,xe.y);var s=ge(ve.x,_e.x,me.x,xe.x),h=ge(ve.y,_e.y,me.y,xe.y);t.width=s-t.x,t.height=h-t.y}else t!==e&&ke.copy(t,e)},ke);function ke(t,e,r,i){r<0&&(t+=r,r=-r),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=r,this.height=i}var Te="silent";function Ce(){Jt(this.event)}var Pe,Me=(yt(Ae,Pe=Ft),Ae.prototype.dispose=function(){},Ae.prototype.setCursor=function(){},Ae);function Ae(){var t=null!==Pe&&Pe.apply(this,arguments)||this;return t.handler=null,t}var Le,De=function(t,e){this.x=t,this.y=e},ze=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Ie=new Se(0,0,0,0),Oe=(yt(Re,Le=Ft),Re.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(O(ze,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},Re.prototype.mousemove=function(t){var e=t.zrX,r=t.zrY,i=Be(this,e,r),n=this._hovered,o=n.target;o&&!o.__zr&&(o=(n=this.findHover(n.x,n.y)).target);var a=this._hovered=i?new De(e,r):this.findHover(e,r),s=a.target,h=this.proxy;h.setCursor&&h.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},Re.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},Re.prototype.resize=function(){this._hovered=new De(0,0)},Re.prototype.dispatch=function(t,e){var r=this[t];r&&r.call(this,e)},Re.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},Re.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},Re.prototype.dispatchToElement=function(t,e,r){var i=(t=t||{}).target;if(!i||!i.silent){for(var n,o,a="on"+e,s={type:e,event:o=r,target:(n=t).target,topTarget:n.topTarget,cancelBubble:!1,offsetX:o.zrX,offsetY:o.zrY,gestureEvent:o.gestureEvent,pinchX:o.pinchX,pinchY:o.pinchY,pinchScale:o.pinchScale,wheelDelta:o.zrDelta,zrByTouch:o.zrByTouch,which:o.which,stop:Ce};i&&(i[a]&&(s.cancelBubble=!!i[a].call(i,s)),i.trigger(e,s),i=i.__hostTarget?i.__hostTarget:i.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(e,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[a]&&t[a].call(t,s),t.trigger&&t.trigger(e,s)}))}},Re.prototype.findHover=function(t,e,r){var i=this.storage.getDisplayList(),n=new De(t,e);if(Fe(i,n,t,e,r),this._pointerSize&&!n.target){for(var o=[],a=this._pointerSize,s=a/2,h=new Se(t-s,e-s,a,a),l=i.length-1;0<=l;l--){var u=i[l];u===r||u.ignore||u.ignoreCoarsePointer||u.parent&&u.parent.ignoreCoarsePointer||(Ie.copy(u.getBoundingRect()),u.transform&&Ie.applyTransform(u.transform),Ie.intersect(h)&&o.push(u))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,f=0;f<s;f+=4)for(var d=0;d<p;d+=c)if(Fe(o,n,t+f*Math.cos(d),e+f*Math.sin(d),r),n.target)return n}return n},Re.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new te);var r=this._gestureMgr;"start"===e&&r.clear();var i,n,o=r.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);"end"===e&&r.clear(),o&&(i=o.type,t.gestureEvent=i,(n=new De).target=o.target,this.dispatchToElement(n,i,o.event))},Re);function Re(t,e,r,i,n){var o=Le.call(this)||this;return o._hovered=new De(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=n,r=r||new Me,o.proxy=null,o.setHandlerProxy(r),o._draggingMgr=new Ot(o),o}function Fe(t,e,r,i,n){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==n&&!a.ignore&&(s=function(t,e,r){if(t[t.rectHover?"rectContain":"contain"](e,r)){for(var i=t,n=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,r))return!1;i.silent&&(n=!0)}var s=i.__hostTarget,i=s||i.parent}return!n||Te}return!1}(a,r,i))&&(e.topTarget||(e.topTarget=a),s!==Te)){e.target=a;break}}}function Be(t,e,r){var i=t.painter;return e<0||e>i.getWidth()||r<0||r>i.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){Oe.prototype[a]=function(t){var e,r,i=t.zrX,n=t.zrY,o=Be(this,i,n);if("mouseup"===a&&o||(r=(e=this.findHover(i,n)).target),"mousedown"===a)this._downEl=r,this._downPoint=[t.zrX,t.zrY],this._upEl=r;else if("mouseup"===a)this._upEl=r;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<Tt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var He=32,Ne=7;function Ee(t,e,r,i){var n=e+1;if(n===r)return 1;if(i(t[n++],t[e])<0){for(;n<r&&i(t[n],t[n-1])<0;)n++;!function(t,e,r){r--;for(;e<r;){var i=t[e];t[e++]=t[r],t[r--]=i}}(t,e,n)}else for(;n<r&&0<=i(t[n],t[n-1]);)n++;return n-e}function We(t,e,r,i,n){for(i===e&&i++;i<r;i++){for(var o,a=t[i],s=e,h=i;s<h;)n(a,t[o=s+h>>>1])<0?h=o:s=1+o;var l=i-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<l;)t[s+l]=t[s+l-1],l--}t[s]=a}}function Xe(t,e,r,i,n,o){var a=0,s=0,h=1;if(0<o(t,e[r+n])){for(s=i-n;h<s&&0<o(t,e[r+n+h]);)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s),a+=n,h+=n}else{for(s=n+1;h<s&&o(t,e[r+n-h])<=0;)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s);var l=a,a=n-h,h=n-l}for(a++;a<h;){var u=a+(h-a>>>1);0<o(t,e[r+u])?a=u+1:h=u}return h}function qe(t,e,r,i,n,o){var a=0,s=0,h=1;if(o(t,e[r+n])<0){for(s=n+1;h<s&&o(t,e[r+n-h])<0;)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s);var l=a,a=n-h,h=n-l}else{for(s=i-n;h<s&&0<=o(t,e[r+n+h]);)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s),a+=n,h+=n}for(a++;a<h;){var u=a+(h-a>>>1);o(t,e[r+u])<0?h=u:a=u+1}return h}function Ye(d,y){var a,s,g=Ne,h=0,v=(d.length,[]);function e(t){var e=a[t],r=s[t],i=a[t+1],n=s[t+1];s[t]=r+n,t===h-3&&(a[t+1]=a[t+2],s[t+1]=s[t+2]),h--;var o=qe(d[i],d,e,r,0,y);e+=o,0!=(r-=o)&&0!==(n=Xe(d[e+r-1],d,i,n,n-1,y))&&(r<=n?function(t,e,r,i){var n=0;for(n=0;n<e;n++)v[n]=d[t+n];var o=0,a=r,s=t;if(d[s++]=d[a++],0==--i){for(n=0;n<e;n++)d[s+n]=v[o+n];return}if(1===e){for(n=0;n<i;n++)d[s+n]=d[a+n];return d[s+i]=v[o]}var h,l,u,c=g;for(;;){l=h=0,u=!1;do{if(y(d[a],v[o])<0){if(d[s++]=d[a++],l++,(h=0)==--i){u=!0;break}}else if(d[s++]=v[o++],h++,l=0,1==--e){u=!0;break}}while((h|l)<c);if(u)break;do{if(0!==(h=qe(d[a],v,o,e,0,y))){for(n=0;n<h;n++)d[s+n]=v[o+n];if(s+=h,o+=h,(e-=h)<=1){u=!0;break}}if(d[s++]=d[a++],0==--i){u=!0;break}if(0!==(l=Xe(v[o],d,a,i,0,y))){for(n=0;n<l;n++)d[s+n]=d[a+n];if(s+=l,a+=l,0===(i-=l)){u=!0;break}}if(d[s++]=v[o++],1==--e){u=!0;break}c--}while(Ne<=h||Ne<=l);if(u)break;c<0&&(c=0),c+=2}if((g=c)<1&&(g=1),1===e){for(n=0;n<i;n++)d[s+n]=d[a+n];d[s+i]=v[o]}else{if(0===e)throw new Error;for(n=0;n<e;n++)d[s+n]=v[o+n]}}:function(t,e,r,i){var n=0;for(n=0;n<i;n++)v[n]=d[r+n];var o=t+e-1,a=i-1,s=r+i-1,h=0,l=0;if(d[s--]=d[o--],0==--e){for(h=s-(i-1),n=0;n<i;n++)d[h+n]=v[n];return}if(1===i){for(l=(s-=e)+1,h=(o-=e)+1,n=e-1;0<=n;n--)d[l+n]=d[h+n];return d[s]=v[a]}var u=g;for(;;){var c=0,p=0,f=!1;do{if(y(v[a],d[o])<0){if(d[s--]=d[o--],c++,(p=0)==--e){f=!0;break}}else if(d[s--]=v[a--],p++,c=0,1==--i){f=!0;break}}while((c|p)<u);if(f)break;do{if(0!==(c=e-qe(v[a],d,t,e,e-1,y))){for(e-=c,l=(s-=c)+1,h=(o-=c)+1,n=c-1;0<=n;n--)d[l+n]=d[h+n];if(0===e){f=!0;break}}if(d[s--]=v[a--],1==--i){f=!0;break}if(0!==(p=i-Xe(d[o],v,0,i,i-1,y))){for(i-=p,l=(s-=p)+1,h=(a-=p)+1,n=0;n<p;n++)d[l+n]=v[h+n];if(i<=1){f=!0;break}}if(d[s--]=d[o--],0==--e){f=!0;break}u--}while(Ne<=c||Ne<=p);if(f)break;u<0&&(u=0),u+=2}(g=u)<1&&(g=1);if(1===i){for(l=(s-=e)+1,h=(o-=e)+1,n=e-1;0<=n;n--)d[l+n]=d[h+n];d[s]=v[a]}else{if(0===i)throw new Error;for(h=s-(i-1),n=0;n<i;n++)d[h+n]=v[n]}})(e,r,i,n)}return a=[],s=[],{mergeRuns:function(){for(;1<h;){var t=h-2;if(1<=t&&s[t-1]<=s[t]+s[t+1]||2<=t&&s[t-2]<=s[t]+s[t-1])s[t-1]<s[t+1]&&t--;else if(s[t]>s[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<h;){var t=h-2;0<t&&s[t-1]<s[t+1]&&t--,e(t)}},pushRun:function(t,e){a[h]=t,s[h]=e,h+=1}}}function je(t,e,r,i){r=r||0;var n=(i=i||t.length)-r;if(!(n<2)){var o=0;if(n<He)We(t,r,i,r+(o=Ee(t,r,i,e)),e);else{var a,s=Ye(t,e),h=function(t){for(var e=0;He<=t;)e|=1&t,t>>=1;return t+e}(n);do{(o=Ee(t,r,i,e))<h&&(h<(a=n)&&(a=h),We(t,r,r+a,r+o,e),o=a),s.pushRun(r,o),s.mergeRuns(),n-=o,r+=o}while(0!==n);s.forceMergeRuns()}}}var Ve=1,Ue=4,Ge=!1;function Ze(){Ge||(Ge=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Ke(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var Qe=($e.prototype.traverse=function(t,e){for(var r=0;r<this._roots.length;r++)this._roots[r].traverse(t,e)},$e.prototype.getDisplayList=function(t,e){e=e||!1;var r=this._displayList;return!t&&r.length||this.updateDisplayList(e),r},$e.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,r=this._displayList,i=0,n=e.length;i<n;i++)this._updateAndAddDisplayable(e[i],null,t);r.length=this._displayListLen,je(r,Ke)},$e.prototype._updateAndAddDisplayable=function(t,e,r){if(!t.ignore||r){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var n=i,o=t;n;)n.parent=o,n.updateTransform(),e.push(n),n=(o=n).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var h=a[s];t.__dirty&&(h.__dirty|=Ve),this._updateAndAddDisplayable(h,e,r)}t.__dirty=0}else{var l=t;e&&e.length?l.__clipPaths=e:l.__clipPaths&&0<l.__clipPaths.length&&(l.__clipPaths=[]),isNaN(l.z)&&(Ze(),l.z=0),isNaN(l.z2)&&(Ze(),l.z2=0),isNaN(l.zlevel)&&(Ze(),l.zlevel=0),this._displayList[this._displayListLen++]=l}var u=t.getDecalElement&&t.getDecalElement();u&&this._updateAndAddDisplayable(u,e,r);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,r);var p=t.getTextContent();p&&this._updateAndAddDisplayable(p,e,r)}},$e.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},$e.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,r=t.length;e<r;e++)this.delRoot(t[e]);else{var i=P(this._roots,t);0<=i&&this._roots.splice(i,1)}},$e.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},$e.prototype.getRoots=function(){return this._roots},$e.prototype.dispose=function(){this._displayList=null,this._roots=null},$e);function $e(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Ke}var Je=c.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},tr={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),-(r*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:r*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-tr.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*tr.bounceIn(2*t):.5*tr.bounceOut(2*t-1)+.5}},er=Math.pow,rr=Math.sqrt,ir=1e-8,nr=1e-4,or=rr(3),ar=1/3,sr=gt(),hr=gt(),lr=gt();function ur(t){return-ir<t&&t<ir}function cr(t){return ir<t||t<-ir}function pr(t,e,r,i,n){var o=1-n;return o*o*(o*t+3*n*e)+n*n*(n*i+3*o*r)}function fr(t,e,r,i,n){var o=1-n;return 3*(((e-t)*o+2*(r-e)*n)*o+(i-r)*n*n)}function dr(t,e,r,i,n,o){var a,s,h,l,u,c,p,f,d,y,g,v,_=i+3*(e-r)-t,m=3*(r-2*e+t),x=3*(e-t),w=t-n,b=m*m-3*_*x,S=m*x-9*_*w,k=x*x-3*m*w,T=0;return ur(b)&&ur(S)?ur(m)?o[0]=0:0<=(y=-x/m)&&y<=1&&(o[T++]=y):ur(a=S*S-4*b*k)?(g=-(s=S/b)/2,0<=(y=-m/_+s)&&y<=1&&(o[T++]=y),0<=g&&g<=1&&(o[T++]=g)):0<a?(u=b*m+1.5*_*(-S-(h=rr(a))),0<=(y=(-m-((l=(l=b*m+1.5*_*(-S+h))<0?-er(-l,ar):er(l,ar))+(u=u<0?-er(-u,ar):er(u,ar))))/(3*_))&&y<=1&&(o[T++]=y)):(c=(2*b*m-3*_*S)/(2*rr(b*b*b)),p=Math.acos(c)/3,y=(-m-2*(f=rr(b))*(d=Math.cos(p)))/(3*_),g=(-m+f*(d+or*Math.sin(p)))/(3*_),v=(-m+f*(d-or*Math.sin(p)))/(3*_),0<=y&&y<=1&&(o[T++]=y),0<=g&&g<=1&&(o[T++]=g),0<=v&&v<=1&&(o[T++]=v)),T}function yr(t,e,r,i,n){var o,a,s,h,l=6*r-12*e+6*t,u=9*e+3*i-3*t-9*r,c=3*e-3*t,p=0;return ur(u)?cr(l)&&0<=(s=-c/l)&&s<=1&&(n[p++]=s):ur(o=l*l-4*u*c)?n[0]=-l/(2*u):0<o&&(h=(-l-(a=rr(o)))/(2*u),0<=(s=(-l+a)/(2*u))&&s<=1&&(n[p++]=s),0<=h&&h<=1&&(n[p++]=h)),p}function gr(t,e,r,i,n,o){var a=(e-t)*n+t,s=(r-e)*n+e,h=(i-r)*n+r,l=(s-a)*n+a,u=(h-s)*n+s,c=(u-l)*n+l;o[0]=t,o[1]=a,o[2]=l,o[3]=c,o[4]=c,o[5]=u,o[6]=h,o[7]=i}function vr(t,e,r,i){var n=1-i;return n*(n*t+2*i*e)+i*i*r}function _r(t,e,r,i){return 2*((1-i)*(e-t)+i*(r-e))}function mr(t,e,r){var i=t+r-2*e;return 0==i?.5:(t-e)/i}function xr(t,e,r,i,n){var o=(e-t)*i+t,a=(r-e)*i+e,s=(a-o)*i+o;n[0]=t,n[1]=o,n[2]=s,n[3]=s,n[4]=a,n[5]=r}var wr=/cubic-bezier\(([0-9,\.e ]+)\)/;function br(t){var e=t&&wr.exec(t);if(e){var r=e[1].split(","),i=+tt(r[0]),n=+tt(r[1]),o=+tt(r[2]),a=+tt(r[3]);if(isNaN(i+n+o+a))return;var s=[];return function(t){return t<=0?0:1<=t?1:dr(0,i,o,1,t,s)&&pr(0,n,a,1,s[0])}}}var Sr=(kr.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var r=this._life,i=t-this._startTime-this._pausedTime,n=i/r;n<0&&(n=0),n=Math.min(n,1);var o=this.easingFunc,a=o?o(n):n;if(this.onframe(a),1===n){if(!this.loop)return!0;var s=i%r;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},kr.prototype.pause=function(){this._paused=!0},kr.prototype.resume=function(){this._paused=!1},kr.prototype.setEasing=function(t){this.easing=t,this.easingFunc=B(t)?t:tr[t]||br(t)},kr);function kr(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||ct,this.ondestroy=t.ondestroy||ct,this.onrestart=t.onrestart||ct,t.easing&&this.setEasing(t.easing)}var Tr=function(t){this.value=t},Cr=(Pr.prototype.insert=function(t){var e=new Tr(t);return this.insertEntry(e),e},Pr.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Pr.prototype.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Pr.prototype.len=function(){return this._len},Pr.prototype.clear=function(){this.head=this.tail=null,this._len=0},Pr);function Pr(){this._len=0}var Mr=(Ar.prototype.put=function(t,e){var r,i,n,o=this._list,a=this._map,s=null;return null==a[t]&&(r=o.len(),i=this._lastRemovedEntry,r>=this._maxSize&&0<r&&(n=o.head,o.remove(n),delete a[n.key],s=n.value,this._lastRemovedEntry=n),i?i.value=e:i=new Tr(e),i.key=t,o.insertEntry(i),a[t]=i),s},Ar.prototype.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},Ar.prototype.clear=function(){this._list.clear(),this._map={}},Ar.prototype.len=function(){return this._list.len()},Ar);function Ar(t){this._list=new Cr,this._maxSize=10,this._map={},this._maxSize=t}var Lr={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Dr(t){return(t=Math.round(t))<0?0:255<t?255:t}function zr(t){return t<0?0:1<t?1:t}function Ir(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Dr(parseFloat(e)/100*255):Dr(parseInt(e,10))}function Or(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?zr(parseFloat(e)/100):zr(parseFloat(e))}function Rr(t,e,r){return r<0?r+=1:1<r&&--r,6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function Fr(t,e,r){return t+(e-t)*r}function Br(t,e,r,i,n){return t[0]=e,t[1]=r,t[2]=i,t[3]=n,t}function Hr(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Nr=new Mr(20),Er=null;function Wr(t,e){Er&&Hr(Er,e),Er=Nr.put(t,Er||e.slice())}function Xr(t,e){if(t){e=e||[];var r=Nr.get(t);if(r)return Hr(e,r);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Lr)return Hr(e,Lr[i]),Wr(t,e),e;var n,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?0<=(n=parseInt(i.slice(1,4),16))&&n<=4095?(Br(e,(3840&n)>>4|(3840&n)>>8,240&n|(240&n)>>4,15&n|(15&n)<<4,5===o?parseInt(i.slice(4),16)/15:1),Wr(t,e),e):void Br(e,0,0,0,1):7===o||9===o?0<=(n=parseInt(i.slice(1,7),16))&&n<=16777215?(Br(e,(16711680&n)>>16,(65280&n)>>8,255&n,9===o?parseInt(i.slice(7),16)/255:1),Wr(t,e),e):void Br(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var h=i.substr(0,a),l=i.substr(a+1,s-(a+1)).split(","),u=1;switch(h){case"rgba":if(4!==l.length)return 3===l.length?Br(e,+l[0],+l[1],+l[2],1):Br(e,0,0,0,1);u=Or(l.pop());case"rgb":return 3<=l.length?(Br(e,Ir(l[0]),Ir(l[1]),Ir(l[2]),3===l.length?u:Or(l[3])),Wr(t,e),e):void Br(e,0,0,0,1);case"hsla":return 4!==l.length?void Br(e,0,0,0,1):(l[3]=Or(l[3]),qr(l,e),Wr(t,e),e);case"hsl":return 3!==l.length?void Br(e,0,0,0,1):(qr(l,e),Wr(t,e),e);default:return}}Br(e,0,0,0,1)}}function qr(t,e){var r=(parseFloat(t[0])%360+360)%360/360,i=Or(t[1]),n=Or(t[2]),o=n<=.5?n*(i+1):n+i-n*i,a=2*n-o;return Br(e=e||[],Dr(255*Rr(a,o,r+1/3)),Dr(255*Rr(a,o,r)),Dr(255*Rr(a,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Yr(t,e,r){if(e&&e.length&&0<=t&&t<=1){r=r||[];var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=e[n],s=e[o],h=i-n;return r[0]=Dr(Fr(a[0],s[0],h)),r[1]=Dr(Fr(a[1],s[1],h)),r[2]=Dr(Fr(a[2],s[2],h)),r[3]=zr(Fr(a[3],s[3],h)),r}}function jr(t,e,r){if(e&&e.length&&0<=t&&t<=1){var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=Xr(e[n]),s=Xr(e[o]),h=i-n,l=Vr([Dr(Fr(a[0],s[0],h)),Dr(Fr(a[1],s[1],h)),Dr(Fr(a[2],s[2],h)),zr(Fr(a[3],s[3],h))],"rgba");return r?{color:l,leftIndex:n,rightIndex:o,value:i}:l}}function Vr(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}}function Ur(t,e){var r=Xr(t);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*e:0}var Gr=Object.freeze({__proto__:null,parse:Xr,lift:function(t,e){var r=Xr(t);if(r){for(var i=0;i<3;i++)r[i]=e<0?r[i]*(1-e)|0:(255-r[i])*e+r[i]|0,255<r[i]?r[i]=255:r[i]<0&&(r[i]=0);return Vr(r,4===r.length?"rgba":"rgb")}},toHex:function(t){var e=Xr(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)},fastLerp:Yr,fastMapToColor:Yr,lerp:jr,mapToColor:jr,modifyHSL:function(t,e,r,i){var n,o=Xr(t);if(t)return o=function(t){if(t){var e,r,i,n,o,a=t[0]/255,s=t[1]/255,h=t[2]/255,l=Math.min(a,s,h),u=Math.max(a,s,h),c=u-l,p=(u+l)/2;0==c?r=e=0:(r=p<.5?c/(u+l):c/(2-u-l),i=((u-a)/6+c/2)/c,n=((u-s)/6+c/2)/c,o=((u-h)/6+c/2)/c,a===u?e=o-n:s===u?e=1/3+i-o:h===u&&(e=2/3+n-i),e<0&&(e+=1),1<e&&--e);var f=[360*e,r,p];return null!=t[3]&&f.push(t[3]),f}}(o),null!=e&&(o[0]=(n=e,(n=Math.round(n))<0?0:360<n?360:n)),null!=r&&(o[1]=Or(r)),null!=i&&(o[2]=Or(i)),Vr(qr(o),"rgba")},modifyAlpha:function(t,e){var r=Xr(t);if(r&&null!=e)return r[3]=zr(e),Vr(r,"rgba")},stringify:Vr,lum:Ur,random:function(){return Vr([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")}}),Zr=Math.round;function Kr(t){var e,r;return t&&"transparent"!==t?"string"==typeof t&&-1<t.indexOf("rgba")&&((r=Xr(t))&&(t="rgb("+r[0]+","+r[1]+","+r[2]+")",e=r[3])):t="none",{color:t,opacity:null==e?1:e}}var Qr=1e-4;function $r(t){return t<Qr&&-Qr<t}function Jr(t){return Zr(1e3*t)/1e3}function ti(t){return Zr(1e4*t)/1e4}var ei={left:"start",right:"end",center:"middle",middle:"middle"};function ri(t){return t&&!!t.image}function ii(t){return ri(t)||(e=t)&&e.svgElement;var e}function ni(t){return"linear"===t.type}function oi(t){return"radial"===t.type}function ai(t){return t&&("linear"===t.type||"radial"===t.type)}function si(t){return"url(#"+t+")"}function hi(t){var e=t.getGlobalScale(),r=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(r)/Math.log(10)),1)}function li(t){var e=t.x||0,r=t.y||0,i=(t.rotation||0)*pt,n=Z(t.scaleX,1),o=Z(t.scaleY,1),a=t.skewX||0,s=t.skewY||0,h=[];return(e||r)&&h.push("translate("+e+"px,"+r+"px)"),i&&h.push("rotate("+i+")"),1===n&&1===o||h.push("scale("+n+","+o+")"),(a||s)&&h.push("skew("+Zr(a*pt)+"deg, "+Zr(s*pt)+"deg)"),h.join(" ")}var ui=c.hasGlobalWindow&&B(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:"undefined"!=typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null},ci=Array.prototype.slice;function pi(t,e,r){return(e-t)*r+t}function fi(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=pi(e[o],r[o],i);return t}function di(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=e[o]+r[o]*i;return t}function yi(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+r[a][s]*i}return t}function gi(t){if(A(t)){var e=t.length;if(A(t[0])){for(var r=[],i=0;i<e;i++)r.push(ci.call(t[i]));return r}return ci.call(t)}return t}function vi(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function _i(t){return 4===t||5===t}function mi(t){return 1===t||2===t}var xi=[0,0,0,0],wi=(bi.prototype.isFinished=function(){return this._finished},bi.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},bi.prototype.needsAnimate=function(){return 1<=this.keyframes.length},bi.prototype.getAdditiveTrack=function(){return this._additiveTrack},bi.prototype.addKeyframe=function(t,e,r){this._needsSort=!0;var i,n,o,a,s=this.keyframes,h=s.length,l=!1,u=6,c=e;A(e)?(1==(u=i=A((a=e)&&a[0])?2:1)&&!N(e[0])||2==i&&!N(e[0][0]))&&(l=!0):N(e)&&!U(e)?u=0:H(e)?isNaN(+e)?(n=Xr(e))&&(c=n,u=3):u=0:j(e)&&((o=I({},c)).colorStops=L(e.colorStops,function(t){return{offset:t.offset,color:Xr(t.color)}}),ni(e)?u=4:oi(e)&&(u=5),c=o),0===h?this.valType=u:u===this.valType&&6!==u||(l=!0),this.discrete=this.discrete||l;var p={time:t,value:c,rawValue:e,percent:0};return r&&(p.easing=r,p.easingFunc=B(r)?r:tr[r]||br(r)),s.push(p),p},bi.prototype.prepare=function(t,e){var r=this.keyframes;this._needsSort&&r.sort(function(t,e){return t.time-e.time});for(var i=this.valType,n=r.length,o=r[n-1],a=this.discrete,s=mi(i),h=_i(i),l=0;l<n;l++){var u=r[l],c=u.value,p=o.value;u.percent=u.time/t,a||(s&&l!==n-1?function(t,e,r){var i=t,n=e;if(i.push&&n.push){var o=i.length,a=n.length;if(o!==a)if(a<o)i.length=a;else for(var s=o;s<a;s++)i.push(1===r?n[s]:ci.call(n[s]));for(var h=i[0]&&i[0].length,s=0;s<i.length;s++)if(1===r)isNaN(i[s])&&(i[s]=n[s]);else for(var l=0;l<h;l++)isNaN(i[s][l])&&(i[s][l]=n[s][l])}}(c,p,i):h&&function(t,e){for(var r=t.length,i=e.length,n=i<r?e:t,o=Math.min(r,i),a=n[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(r,i);s++)n.push({offset:a.offset,color:a.color.slice()})}(c.colorStops,p.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var f=r[0].value,l=0;l<n;l++)0===i?r[l].additiveValue=r[l].value-f:3===i?r[l].additiveValue=di([],r[l].value,f,-1):mi(i)&&(r[l].additiveValue=(1===i?di:yi)([],r[l].value,f,-1))}},bi.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var r,i,n,o,a,s,h,l,u,c=null!=this._additiveTrack,p=c?"additiveValue":"value",f=this.valType,d=this.keyframes,y=d.length,g=this.propName,v=3===f,_=this._lastFr,m=Math.min;if(1===y)r=i=d[0];else{if(e<0)x=0;else if(e<this._lastFrP){for(var x=m(_+1,y-1);0<=x&&!(d[x].percent<=e);x--);x=m(x,y-2)}else{for(x=_;x<y&&!(d[x].percent>e);x++);x=m(x-1,y-2)}i=d[x+1],r=d[x]}r&&i&&(this._lastFr=x,this._lastFrP=e,n=i.percent-r.percent,o=0==n?1:m((e-r.percent)/n,1),i.easingFunc&&(o=i.easingFunc(o)),a=c?this._additiveValue:v?xi:t[g],!mi(f)&&!v||a||(a=this._additiveValue=[]),this.discrete?t[g]=o<1?r.rawValue:i.rawValue:mi(f)?(1===f?fi:function(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=pi(e[a][s],r[a][s],i)}return t})(a,r[p],i[p],o):_i(f)?(s=r[p],h=i[p],l=4===f,t[g]={type:l?"linear":"radial",x:pi(s.x,h.x,o),y:pi(s.y,h.y,o),colorStops:L(s.colorStops,function(t,e){var r=h.colorStops[e];return{offset:pi(t.offset,r.offset,o),color:vi(fi([],t.color,r.color,o))}}),global:h.global},l?(t[g].x2=pi(s.x2,h.x2,o),t[g].y2=pi(s.y2,h.y2,o)):t[g].r=pi(s.r,h.r,o)):v?(fi(a,r[p],i[p],o),c||(t[g]=vi(a))):(u=pi(r[p],i[p],o),c?this._additiveValue=u:t[g]=u),c&&this._addToTarget(t))}},bi.prototype._addToTarget=function(t){var e=this.valType,r=this.propName,i=this._additiveValue;0===e?t[r]=t[r]+i:3===e?(Xr(t[r],xi),di(xi,xi,i,1),t[r]=vi(xi)):1===e?di(t[r],t[r],i,1):2===e&&yi(t[r],t[r],i,1)},bi);function bi(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}var Si=(ki.prototype.getMaxTime=function(){return this._maxTime},ki.prototype.getDelay=function(){return this._delay},ki.prototype.getLoop=function(){return this._loop},ki.prototype.getTarget=function(){return this._target},ki.prototype.changeTarget=function(t){this._target=t},ki.prototype.when=function(t,e,r){return this.whenWithKeys(t,e,F(e),r)},ki.prototype.whenWithKeys=function(t,e,r,i){for(var n=this._tracks,o=0;o<r.length;o++){var a=r[o],s=n[a];if(!s){s=n[a]=new wi(a);var h,l,u=void 0,c=this._getAdditiveTrack(a);if(c?(u=(l=(h=c.keyframes)[h.length-1])&&l.value,3===c.valType&&u&&(u=vi(u))):u=this._target[a],null==u)continue;0<t&&s.addKeyframe(0,gi(u),i),this._trackKeys.push(a)}s.addKeyframe(t,gi(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},ki.prototype.pause=function(){this._clip.pause(),this._paused=!0},ki.prototype.resume=function(){this._clip.resume(),this._paused=!1},ki.prototype.isPaused=function(){return!!this._paused},ki.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},ki.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,r=0;r<e;r++)t[r].call(this)},ki.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var r=0;r<e.length;r++)e[r].call(this)},ki.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,r=0;r<e.length;r++)t[e[r]].setFinished()},ki.prototype._getAdditiveTrack=function(t){var e,r=this._additiveAnimators;if(r)for(var i=0;i<r.length;i++){var n=r[i].getTrack(t);n&&(e=n)}return e},ki.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],r=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var n,s=this._trackKeys[i],h=this._tracks[s],l=this._getAdditiveTrack(s),u=h.keyframes,c=u.length;h.prepare(r,l),h.needsAnimate()&&(!this._allowDiscrete&&h.discrete?((n=u[c-1])&&(o._target[h.propName]=n.rawValue),h.setFinished()):a.push(h))}return a.length||this._force?(e=new Sr({life:r,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var r=!1,i=0;i<e.length;i++)if(e[i]._clip){r=!0;break}r||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var n=o._onframeCbs;if(n)for(i=0;i<n.length;i++)n[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},ki.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},ki.prototype.delay=function(t){return this._delay=t,this},ki.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},ki.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},ki.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},ki.prototype.getClip=function(){return this._clip},ki.prototype.getTrack=function(t){return this._tracks[t]},ki.prototype.getTracks=function(){var e=this;return L(this._trackKeys,function(t){return e._tracks[t]})},ki.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var r=this._tracks,i=this._trackKeys,n=0;n<t.length;n++){var o=r[t[n]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,n=0;n<i.length;n++)if(!r[i[n]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},ki.prototype.saveTo=function(t,e,r){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var n,o,a=e[i],s=this._tracks[a];s&&!s.isFinished()&&(o=(n=s.keyframes)[r?0:n.length-1])&&(t[a]=gi(o.rawValue))}}},ki.prototype.__changeFinalValue=function(t,e){e=e||F(t);for(var r=0;r<e.length;r++){var i,n,o=e[r],a=this._tracks[o];a&&1<(i=a.keyframes).length&&(n=i.pop(),a.addKeyframe(n.time,t[o]),a.prepare(this._maxTime,a.getAdditiveTrack()))}},ki);function ki(t,e,r,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?b("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=r)}function Ti(){return(new Date).getTime()}var Ci,Pi=(yt(Mi,Ci=Ft),Mi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Mi.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},Mi.prototype.removeClip=function(t){var e,r;t.animation&&(e=t.prev,r=t.next,e?e.next=r:this._head=r,r?r.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Mi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Mi.prototype.update=function(t){for(var e=Ti()-this._pausedTime,r=e-this._time,i=this._head;i;)var n=i.next,i=(i.step(e,r)&&(i.ondestroy(),this.removeClip(i)),n);this._time=e,t||(this.trigger("frame",r),this.stage.update&&this.stage.update())},Mi.prototype._startLoop=function(){var e=this;this._running=!0,Je(function t(){e._running&&(Je(t),e._paused||e.update())})},Mi.prototype.start=function(){this._running||(this._time=Ti(),this._pausedTime=0,this._startLoop())},Mi.prototype.stop=function(){this._running=!1},Mi.prototype.pause=function(){this._paused||(this._pauseStart=Ti(),this._paused=!0)},Mi.prototype.resume=function(){this._paused&&(this._pausedTime+=Ti()-this._pauseStart,this._paused=!1)},Mi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Mi.prototype.isFinished=function(){return null==this._head},Mi.prototype.animate=function(t,e){e=e||{},this.start();var r=new Si(t,e.loop);return this.addAnimator(r),r},Mi);function Mi(t){var e=Ci.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,t=t||{},e.stage=t.stage||{},e}var Ai,Li,Di=c.domSupported,zi=(Li={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Ai=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:L(Ai,function(t){var e=t.replace("mouse","pointer");return Li.hasOwnProperty(e)?e:t})}),Ii=["mousemove","mouseup"],Oi=["pointermove","pointerup"],Ri=!1;function Fi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Bi(t){t&&(t.zrByTouch=!0)}function Hi(t,e){for(var r=e,i=!1;r&&9!==r.nodeType&&!(i=r.domBelongToZr||r!==e&&r===t.painterRoot);)r=r.parentNode;return i}var Ni=function(t,e){this.stopPropagation=ct,this.stopImmediatePropagation=ct,this.preventDefault=ct,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Ei={mousedown:function(t){t=$t(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=$t(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=$t(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){Hi(this,(t=$t(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Ri=!0,t=$t(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Ri||(t=$t(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Bi(t=$t(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Ei.mousemove.call(this,t),Ei.mousedown.call(this,t)},touchmove:function(t){Bi(t=$t(this.dom,t)),this.handler.processGesture(t,"change"),Ei.mousemove.call(this,t)},touchend:function(t){Bi(t=$t(this.dom,t)),this.handler.processGesture(t,"end"),Ei.mouseup.call(this,t),new Date-this.__lastTouchMoment<300&&Ei.click.call(this,t)},pointerdown:function(t){Ei.mousedown.call(this,t)},pointermove:function(t){Fi(t)||Ei.mousemove.call(this,t)},pointerup:function(t){Ei.mouseup.call(this,t)},pointerout:function(t){Fi(t)||Ei.mouseout.call(this,t)}};O(["click","dblclick","contextmenu"],function(e){Ei[e]=function(t){t=$t(this.dom,t),this.trigger(e,t)}});var Wi={pointermove:function(t){Fi(t)||Wi.mousemove.call(this,t)},pointerup:function(t){Wi.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function Xi(i,n){var o=n.domHandlers;c.pointerEventsSupported?O(zi.pointer,function(e){Yi(n,e,function(t){o[e].call(i,t)})}):(c.touchEventsSupported&&O(zi.touch,function(r){Yi(n,r,function(t){var e;o[r].call(i,t),(e=n).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),O(zi.mouse,function(e){Yi(n,e,function(t){t=Qt(t),n.touching||o[e].call(i,t)})}))}function qi(n,o){function t(i){Yi(o,i,function(t){var e,r;t=Qt(t),Hi(n,t.target)||(r=t,t=$t((e=n).dom,new Ni(e,r),!0),o.domHandlers[i].call(n,t))},{capture:!0})}c.pointerEventsSupported?O(Oi,t):c.touchEventsSupported||O(Ii,t)}function Yi(t,e,r,i){var n,o,a,s;t.mounted[e]=r,t.listenerOpts[e]=i,n=t.domTarget,o=e,a=r,s=i,n.addEventListener(o,a,s)}function ji(t){var e,r,i,n,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,i=o[r=a],n=t.listenerOpts[a],e.removeEventListener(r,i,n));t.mounted={}}var Vi,Ui=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},Gi=(yt(Zi,Vi=Ft),Zi.prototype.dispose=function(){ji(this._localHandlerScope),Di&&ji(this._globalHandlerScope)},Zi.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},Zi.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Di&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?qi(this,e):ji(e))},Zi);function Zi(t,e){var r=Vi.call(this)||this;return r.__pointerCapturing=!1,r.dom=t,r.painterRoot=e,r._localHandlerScope=new Ui(t,Ei),Di&&(r._globalHandlerScope=new Ui(document,Wi)),Xi(r,r._localHandlerScope),r}var Ki=1;c.hasGlobalWindow&&(Ki=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Qi=Ki,$i="#333",Ji="#ccc",tn=oe;function en(t){return 5e-5<t||t<-5e-5}var rn,nn=[],on=[],an=ne(),sn=Math.abs,hn=(ln.prototype.getLocalTransform=function(t){return ln.getLocalTransform(this,t)},ln.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},ln.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},ln.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},ln.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},ln.prototype.needLocalTransform=function(){return en(this.rotation)||en(this.x)||en(this.y)||en(this.scaleX-1)||en(this.scaleY-1)||en(this.skewX)||en(this.skewY)},ln.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),r=this.transform;e||t?(r=r||ne(),e?this.getLocalTransform(r):tn(r),t&&(e?se(r,t,r):ae(r,t)),this.transform=r,this._resolveGlobalScaleRatio(r)):r&&tn(r)},ln.prototype._resolveGlobalScaleRatio=function(t){var e,r,i,n,o=this.globalScaleRatio;null!=o&&1!==o&&(this.getGlobalScale(nn),i=((nn[0]-(e=nn[0]<0?-1:1))*o+e)/nn[0]||0,n=((nn[1]-(r=nn[1]<0?-1:1))*o+r)/nn[1]||0,t[0]*=i,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||ne(),ce(this.invTransform,t)},ln.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},ln.prototype.setLocalTransform=function(t){var e,r,i,n;t&&(n=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),r=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(r),n=Math.sqrt(n),this.skewX=r,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=n,this.scaleY=i,this.originX=0,this.originY=0)},ln.prototype.decomposeTransform=function(){var t,e,r,i;this.transform&&(t=this.parent,e=this.transform,t&&t.transform&&(se(on,t.invTransform,e),e=on),r=this.originX,i=this.originY,(r||i)&&(an[4]=r,an[5]=i,se(on,e,an),on[4]-=r,on[5]-=i,e=on),this.setLocalTransform(e))},ln.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},ln.prototype.transformCoordToLocal=function(t,e){var r=[t,e],i=this.invTransform;return i&&At(r,r,i),r},ln.prototype.transformCoordToGlobal=function(t,e){var r=[t,e],i=this.transform;return i&&At(r,r,i),r},ln.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<sn(t[0]-1)&&1e-10<sn(t[3]-1)?Math.sqrt(sn(t[0]*t[3]-t[2]*t[1])):1},ln.prototype.copyTransform=function(t){cn(this,t)},ln.getLocalTransform=function(t,e){e=e||[];var r,i,n=t.originX||0,o=t.originY||0,a=t.scaleX,s=t.scaleY,h=t.anchorX,l=t.anchorY,u=t.rotation||0,c=t.x,p=t.y,f=t.skewX?Math.tan(t.skewX):0,d=t.skewY?Math.tan(-t.skewY):0;return n||o||h||l?(r=n+h,i=o+l,e[4]=-r*a-f*i*s,e[5]=-i*s-d*r*a):e[4]=e[5]=0,e[0]=a,e[3]=s,e[1]=d*a,e[2]=f*s,u&&le(e,e,u),e[4]+=n+c,e[5]+=o+p,e},ln.initDefaultProps=((rn=ln.prototype).scaleX=rn.scaleY=rn.globalScaleRatio=1,void(rn.x=rn.y=rn.originX=rn.originY=rn.skewX=rn.skewY=rn.rotation=rn.anchorX=rn.anchorY=0)),ln);function ln(){}var un=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function cn(t,e){for(var r=0;r<un.length;r++){var i=un[r];t[i]=e[i]}}var pn={};function fn(t,e){var r=pn[e=e||E],i=(r=r||(pn[e]=new Mr(500))).get(t);return null==i&&(i=f.measureText(t,e).width,r.put(t,i)),i}function dn(t,e,r,i){var n=fn(t,e),o=vn(e),a=yn(0,n,r),s=gn(0,o,i);return new Se(a,s,n,o)}function yn(t,e,r){return"right"===r?t-=e:"center"===r&&(t-=e/2),t}function gn(t,e,r){return"middle"===r?t-=e/2:"bottom"===r&&(t-=e),t}function vn(t){return fn("国",t)}function _n(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}var mn,xn="__zr_normal__",wn=un.concat(["ignore"]),bn=D(un,function(t,e){return t[e]=!0,t},{ignore:!1}),Sn={},kn=new Se(0,0,0,0),Tn=(Cn.prototype._init=function(t){this.attr(t)},Cn.prototype.drift=function(t,e,r){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},Cn.prototype.beforeUpdate=function(){},Cn.prototype.afterUpdate=function(){},Cn.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},Cn.prototype.updateInnerText=function(t){var e,r,i,n,o,a,s,h,l,u,c,p,f,d,y,g,v,_=this._textContent;!_||_.ignore&&!t||(this.textConfig||(this.textConfig={}),r=(e=this.textConfig).local,o=n=void 0,a=!1,(i=_.innerTransformable).parent=r?this:null,c=!1,i.copyTransform(_),null!=e.position&&(s=kn,e.layoutRect?s.copy(e.layoutRect):s.copy(this.getBoundingRect()),r||s.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Sn,e,s):function(t,e,r){var i=e.position||"inside",n=null!=e.distance?e.distance:5,o=r.height,a=r.width,s=o/2,h=r.x,l=r.y,u="left",c="top";if(i instanceof Array)h+=_n(i[0],r.width),l+=_n(i[1],r.height),c=u=null;else switch(i){case"left":h-=n,l+=s,u="right",c="middle";break;case"right":h+=n+a,l+=s,c="middle";break;case"top":h+=a/2,l-=n,u="center",c="bottom";break;case"bottom":h+=a/2,l+=o+n,u="center";break;case"inside":h+=a/2,l+=s,u="center",c="middle";break;case"insideLeft":h+=n,l+=s,c="middle";break;case"insideRight":h+=a-n,l+=s,u="right",c="middle";break;case"insideTop":h+=a/2,l+=n,u="center";break;case"insideBottom":h+=a/2,l+=o-n,u="center",c="bottom";break;case"insideTopLeft":h+=n,l+=n;break;case"insideTopRight":h+=a-n,l+=n,u="right";break;case"insideBottomLeft":h+=n,l+=o-n,c="bottom";break;case"insideBottomRight":h+=a-n,l+=o-n,u="right",c="bottom"}(t=t||{}).x=h,t.y=l,t.align=u,t.verticalAlign=c}(Sn,e,s),i.x=Sn.x,i.y=Sn.y,n=Sn.align,o=Sn.verticalAlign,(h=e.origin)&&null!=e.rotation&&(u=l=void 0,u="center"===h?(l=.5*s.width,.5*s.height):(l=_n(h[0],s.width),_n(h[1],s.height)),c=!0,i.originX=-i.x+l+(r?0:s.x),i.originY=-i.y+u+(r?0:s.y))),null!=e.rotation&&(i.rotation=e.rotation),(p=e.offset)&&(i.x+=p[0],i.y+=p[1],c||(i.originX=-p[0],i.originY=-p[1])),f=null==e.inside?"string"==typeof e.position&&0<=e.position.indexOf("inside"):e.inside,d=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),v=g=y=void 0,f&&this.canBeInsideText()?(y=e.insideFill,g=e.insideStroke,null!=y&&"auto"!==y||(y=this.getInsideTextFill()),null!=g&&"auto"!==g||(g=this.getInsideTextStroke(y),v=!0)):(y=e.outsideFill,g=e.outsideStroke,null!=y&&"auto"!==y||(y=this.getOutsideFill()),null!=g&&"auto"!==g||(g=this.getOutsideStroke(y),v=!0)),(y=y||"#000")===d.fill&&g===d.stroke&&v===d.autoStroke&&n===d.align&&o===d.verticalAlign||(a=!0,d.fill=y,d.stroke=g,d.autoStroke=v,d.align=n,d.verticalAlign=o,_.setDefaultTextStyle(d)),_.__dirty|=Ve,a&&_.dirtyStyle(!0))},Cn.prototype.canBeInsideText=function(){return!0},Cn.prototype.getInsideTextFill=function(){return"#fff"},Cn.prototype.getInsideTextStroke=function(t){return"#000"},Cn.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Ji:$i},Cn.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),r="string"==typeof e&&Xr(e),i=(r=r||[255,255,255,1])[3],n=this.__zr.isDarkMode(),o=0;o<3;o++)r[o]=r[o]*i+(n?0:255)*(1-i);return r[3]=1,Vr(r,"rgba")},Cn.prototype.traverse=function(t,e){},Cn.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},I(this.extra,e)):this[t]=e},Cn.prototype.hide=function(){this.ignore=!0,this.markRedraw()},Cn.prototype.show=function(){this.ignore=!1,this.markRedraw()},Cn.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(W(t))for(var r=F(t),i=0;i<r.length;i++){var n=r[i];this.attrKV(n,t[n])}return this.markRedraw(),this},Cn.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,r=0;r<this.animators.length;r++){var i,n,o=this.animators[r],a=o.__fromStateTransition;o.getLoop()||a&&a!==xn||(n=(i=o.targetName)?e[i]:e,o.saveTo(n))}},Cn.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,wn)},Cn.prototype._savePrimaryToNormal=function(t,e,r){for(var i=0;i<r.length;i++){var n=r[i];null==t[n]||n in e||(e[n]=this[n])}},Cn.prototype.hasState=function(){return 0<this.currentStates.length},Cn.prototype.getState=function(t){return this.states[t]},Cn.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},Cn.prototype.clearStates=function(t){this.useState(xn,!1,t)},Cn.prototype.useState=function(t,e,r,i){var n=t===xn;if(this.hasState()||!n){var o,a=this.currentStates,s=this.stateTransition;if(!(0<=P(a,t))||!e&&1!==a.length){if(this.stateProxy&&!n&&(o=this.stateProxy(t)),(o=o||this.states&&this.states[t])||n){n||this.saveCurrentToNormalState(o);var h=!!(o&&o.hoverLayer||i);h&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,o,this._normalState,e,!r&&!this.__inHover&&s&&0<s.duration,s);var l=this._textContent,u=this._textGuide;return l&&l.useState(t,e,r,h),u&&u.useState(t,e,r,h),n?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Ve),o}b("State "+t+" not exists.")}}},Cn.prototype.useStates=function(t,e,r){if(t.length){var i=[],n=this.currentStates,o=t.length,a=o===n.length;if(a)for(var s=0;s<o;s++)if(t[s]!==n[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var h=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(h,t)),(l=l||this.states[h])&&i.push(l)}var u=i[o-1],c=!!(u&&u.hoverLayer||r);c&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),f=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&f&&0<f.duration,f);var d=this._textContent,y=this._textGuide;d&&d.useStates(t,e,c),y&&y.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Ve)}else this.clearStates()},Cn.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},Cn.prototype.removeState=function(t){var e,r=P(this.currentStates,t);0<=r&&((e=this.currentStates.slice()).splice(r,1),this.useStates(e))},Cn.prototype.replaceState=function(t,e,r){var i=this.currentStates.slice(),n=P(i,t),o=0<=P(i,e);0<=n?o?i.splice(n,1):i[n]=e:r&&!o&&i.push(e),this.useStates(i)},Cn.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},Cn.prototype._mergeStates=function(t){for(var e,r={},i=0;i<t.length;i++){var n=t[i];I(r,n),n.textConfig&&I(e=e||{},n.textConfig)}return e&&(r.textConfig=e),r},Cn.prototype._applyStateObj=function(t,e,r,i,n,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=I({},i?this.textConfig:r.textConfig),I(this.textConfig,e.textConfig)):a&&r.textConfig&&(this.textConfig=r.textConfig);for(var s={},h=!1,l=0;l<wn.length;l++){var u=wn[l],c=n&&bn[u];e&&null!=e[u]?c?(h=!0,s[u]=e[u]):this[u]=e[u]:a&&null!=r[u]&&(c?(h=!0,s[u]=r[u]):this[u]=r[u])}if(!n)for(l=0;l<this.animators.length;l++){var p=this.animators[l],f=p.targetName;p.getLoop()||p.__changeFinalValue(f?(e||r)[f]:e||r)}h&&this._transitionState(t,s,o)},Cn.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},Cn.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},Cn.prototype.getClipPath=function(){return this._clipPath},Cn.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},Cn.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},Cn.prototype.getTextContent=function(){return this._textContent},Cn.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new hn,this._attachComponent(t),this._textContent=t,this.markRedraw())},Cn.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),I(this.textConfig,t),this.markRedraw()},Cn.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},Cn.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},Cn.prototype.getTextGuideLine=function(){return this._textGuide},Cn.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},Cn.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},Cn.prototype.markRedraw=function(){this.__dirty|=Ve;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},Cn.prototype.dirty=function(){this.markRedraw()},Cn.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,r=this._textGuide;e&&(e.__inHover=t),r&&(r.__inHover=t)},Cn.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},Cn.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},Cn.prototype.animate=function(t,e,r){var i=t?this[t]:this,n=new Si(i,e,r);return t&&(n.targetName=t),this.addAnimator(n,t),n},Cn.prototype.addAnimator=function(r,t){var e=this.__zr,i=this;r.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=P(t,r);0<=e&&t.splice(e,1)}),this.animators.push(r),e&&e.animation.addAnimator(r),e&&e.wakeUp()},Cn.prototype.updateDuringAnimation=function(t){this.markRedraw()},Cn.prototype.stopAnimation=function(t,e){for(var r=this.animators,i=r.length,n=[],o=0;o<i;o++){var a=r[o];t&&t!==a.scope?n.push(a):a.stop(e)}return this.animators=n,this},Cn.prototype.animateTo=function(t,e,r){Mn(this,t,e,r)},Cn.prototype.animateFrom=function(t,e,r){Mn(this,t,e,r,!0)},Cn.prototype._transitionState=function(t,e,r,i){for(var n=Mn(this,e,r,i),o=0;o<n.length;o++)n[o].__fromStateTransition=t},Cn.prototype.getBoundingRect=function(){return null},Cn.prototype.getPaintRect=function(){return null},Cn.initDefaultProps=((mn=Cn.prototype).type="element",mn.name="",mn.ignore=mn.silent=mn.isGroup=mn.draggable=mn.dragging=mn.ignoreClip=mn.__inHover=!1,mn.__dirty=Ve,void(Object.defineProperty&&(Pn("position","_legacyPos","x","y"),Pn("scale","_legacyScale","scaleX","scaleY"),Pn("origin","_legacyOrigin","originX","originY")))),Cn);function Cn(t){this.id=w(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Pn(t,e,r,i){function n(e,t){Object.defineProperty(t,0,{get:function(){return e[r]},set:function(t){e[r]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(mn,t,{get:function(){var t;return this[e]||(t=this[e]=[],n(this,t)),this[e]},set:function(t){this[r]=t[0],this[i]=t[1],this[e]=t,n(this,t)}})}function Mn(t,e,r,i,n){var o=[];!function t(e,r,i,n,o,a,s,h){var l=F(n);var u=o.duration;var c=o.delay;var p=o.additive;var f=o.setToFinal;var d=!W(a);var y=e.animators;var g=[];for(var v=0;v<l.length;v++){var _=l[v],m=n[_];if(null!=m&&null!=i[_]&&(d||a[_]))if(!W(m)||A(m)||j(m))g.push(_);else{if(r){h||(i[_]=m,e.updateDuringAnimation(r));continue}t(e,_,i[_],m,o,a&&a[_],s,h)}else h||(i[_]=m,e.updateDuringAnimation(r),g.push(_))}var x=g.length;if(!p&&x)for(var w=0;w<y.length;w++){var b,S=y[w];S.targetName!==r||S.stopTracks(g)&&(b=P(y,S),y.splice(b,1))}o.force||(g=R(g,function(t){return e=n[t],r=i[t],!(e===r||A(e)&&A(r)&&function(t,e){var r=t.length;if(r!==e.length)return;for(var i=0;i<r;i++)if(t[i]!==e[i])return;return 1}(e,r));var e,r}),x=g.length);if(0<x||o.force&&!s.length){var k=void 0,T=void 0,C=void 0;if(h){T={},f&&(k={});for(var w=0;w<x;w++){var _=g[w];T[_]=i[_],f?k[_]=n[_]:i[_]=n[_]}}else if(f){C={};for(var w=0;w<x;w++){var _=g[w];C[_]=gi(i[_]),Ln(i,n,_)}}var S=new Si(i,!1,!1,p?R(y,function(t){return t.targetName===r}):null);S.targetName=r,o.scope&&(S.scope=o.scope),f&&k&&S.whenWithKeys(0,k,g),C&&S.whenWithKeys(0,C,g),S.whenWithKeys(null==u?500:u,h?T:n,g).delay(c||0),e.addAnimator(S,r),s.push(S)}}(t,"",t,e,r=r||{},i,o,n);function a(){l=!0,--h<=0&&(l?u&&u():c&&c())}function s(){--h<=0&&(l?u&&u():c&&c())}var h=o.length,l=!1,u=r.done,c=r.aborted;h||u&&u(),0<o.length&&r.during&&o[0].during(function(t,e){r.during(e)});for(var p=0;p<o.length;p++){var f=o[p];f.done(a),f.aborted(s),r.force&&f.duration(r.duration),f.start(r.easing)}return o}function An(t,e,r){for(var i=0;i<r;i++)t[i]=e[i]}function Ln(t,e,r){if(A(e[r]))if(A(t[r])||(t[r]=[]),q(e[r])){var i=e[r].length;t[r].length!==i&&(t[r]=new e[r].constructor(i),An(t[r],e[r],i))}else{var n=e[r],o=t[r],a=n.length;if(A(n[0]))for(var s=n[0].length,h=0;h<a;h++)o[h]?An(o[h],n[h],s):o[h]=Array.prototype.slice.call(n[h]);else An(o,n,a);o.length=n.length}else t[r]=e[r]}M(Tn,Ft),M(Tn,hn);var Dn,zn=(yt(In,Dn=Tn),In.prototype.childrenRef=function(){return this._children},In.prototype.children=function(){return this._children.slice()},In.prototype.childAt=function(t){return this._children[t]},In.prototype.childOfName=function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},In.prototype.childCount=function(){return this._children.length},In.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},In.prototype.addBefore=function(t,e){var r,i;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(i=(r=this._children).indexOf(e))&&(r.splice(i,0,t),this._doAdd(t)),this},In.prototype.replace=function(t,e){var r=P(this._children,t);return 0<=r&&this.replaceAt(e,r),this},In.prototype.replaceAt=function(t,e){var r,i=this._children,n=i[e];return t&&t!==this&&t.parent!==this&&t!==n&&(i[e]=t,n.parent=null,(r=this.__zr)&&n.removeSelfFromZr(r),this._doAdd(t)),this},In.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},In.prototype.remove=function(t){var e=this.__zr,r=this._children,i=P(r,t);return i<0||(r.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},In.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,r=0;r<t.length;r++){var i=t[r];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},In.prototype.eachChild=function(t,e){for(var r=this._children,i=0;i<r.length;i++){var n=r[i];t.call(e,n,i)}return this},In.prototype.traverse=function(t,e){for(var r=0;r<this._children.length;r++){var i=this._children[r],n=t.call(e,i);i.isGroup&&!n&&i.traverse(t,e)}return this},In.prototype.addSelfToZr=function(t){Dn.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},In.prototype.removeSelfFromZr=function(t){Dn.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},In.prototype.getBoundingRect=function(t){for(var e=new Se(0,0,0,0),r=t||this._children,i=[],n=null,o=0;o<r.length;o++){var a,s,h=r[o];h.ignore||h.invisible||(a=h.getBoundingRect(),(s=h.getLocalTransform(i))?(Se.applyTransform(e,a,s),(n=n||e.clone()).union(e)):(n=n||a.clone()).union(a))}return n||e},In);function In(t){var e=Dn.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}zn.prototype.type="group";var On={},Rn={};var Fn=(Bn.prototype.add=function(t){t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},Bn.prototype.remove=function(t){t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},Bn.prototype.configLayer=function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh()},Bn.prototype.setBackgroundColor=function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return Ur(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,r=0,i=e.length,n=0;n<i;n++)r+=Ur(e[n].color,1);return(r/=i)<.4}return!1}(t)},Bn.prototype.getBackgroundColor=function(){return this._backgroundColor},Bn.prototype.setDarkMode=function(t){this._darkMode=t},Bn.prototype.isDarkMode=function(){return this._darkMode},Bn.prototype.refreshImmediately=function(t){t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},Bn.prototype.refresh=function(){this._needsRefresh=!0,this.animation.start()},Bn.prototype.flush=function(){this._flush(!1)},Bn.prototype._flush=function(t){var e,r=Ti();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=Ti();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-r})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},Bn.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},Bn.prototype.wakeUp=function(){this.animation.start(),this._stillFrameAccum=0},Bn.prototype.refreshHover=function(){this._needsRefreshHover=!0},Bn.prototype.refreshHoverImmediately=function(){this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover()},Bn.prototype.resize=function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},Bn.prototype.clearAnimation=function(){this.animation.clear()},Bn.prototype.getWidth=function(){return this.painter.getWidth()},Bn.prototype.getHeight=function(){return this.painter.getHeight()},Bn.prototype.setCursorStyle=function(t){this.handler.setCursorStyle(t)},Bn.prototype.findHover=function(t,e){return this.handler.findHover(t,e)},Bn.prototype.on=function(t,e,r){return this.handler.on(t,e,r),this},Bn.prototype.off=function(t,e){this.handler.off(t,e)},Bn.prototype.trigger=function(t,e){this.handler.trigger(t,e)},Bn.prototype.clear=function(){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof zn&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()},Bn.prototype.dispose=function(){var t;this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,t=this.id,delete Rn[t]},Bn);function Bn(t,e,r){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,r=r||{},this.dom=e,this.id=t;var n=new Qe,o=r.renderer||"canvas";On[o]||(o=F(On)[0]),r.useDirtyRect=null!=r.useDirtyRect&&r.useDirtyRect;var a=new On[o](e,n,r,t),s=r.ssr||a.ssrOnly;this.storage=n,this.painter=a;var h,l=c.node||c.worker||s?null:new Gi(a.getViewportRoot(),a.root),u=r.useCoarsePointer;(null==u||"auto"===u?c.touchEventsSupported:!!u)&&(h=Z(r.pointerSize,44)),this.handler=new Oe(n,a,l,a.root,h),this.animation=new Pi({stage:{update:s?null:function(){return i._flush(!0)}}}),s||this.animation.start()}function Hn(t,e){On[t]=e}var Nn="__zr_style_"+Math.round(10*Math.random()),En={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},Wn={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};En[Nn]=!0;var Xn,qn,Yn=["z","z2","invisible"],jn=["invisible"],Vn=(yt(Un,Xn=Tn),Un.prototype._init=function(t){for(var e=F(t),r=0;r<e.length;r++){var i=e[r];"style"===i?this.useStyle(t[i]):Xn.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},Un.prototype.beforeBrush=function(){},Un.prototype.afterBrush=function(){},Un.prototype.innerBeforeBrush=function(){},Un.prototype.innerAfterBrush=function(){},Un.prototype.shouldBePainted=function(t,e,r,i){var n=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,r){return Gn.copy(t.getBoundingRect()),t.transform&&Gn.applyTransform(t.transform),Zn.width=e,Zn.height=r,!Gn.intersect(Zn)}(this,t,e)||n&&!n[0]&&!n[3])return!1;if(r&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},Un.prototype.contain=function(t,e){return this.rectContain(t,e)},Un.prototype.traverse=function(t,e){t.call(e,this)},Un.prototype.rectContain=function(t,e){var r=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(r[0],r[1])},Un.prototype.getPaintRect=function(){var t,e,r,i,n,o,a,s=this._paintRect;return this._paintRect&&!this.__dirty||(t=this.transform,e=this.getBoundingRect(),i=(r=this.style).shadowBlur||0,n=r.shadowOffsetX||0,o=r.shadowOffsetY||0,s=this._paintRect||(this._paintRect=new Se(0,0,0,0)),t?Se.applyTransform(s,e,t):s.copy(e),(i||n||o)&&(s.width+=2*i+Math.abs(n),s.height+=2*i+Math.abs(o),s.x=Math.min(s.x,s.x+n-i),s.y=Math.min(s.y,s.y+o-i)),a=this.dirtyRectTolerance,s.isZero()||(s.x=Math.floor(s.x-a),s.y=Math.floor(s.y-a),s.width=Math.ceil(s.width+1+2*a),s.height=Math.ceil(s.height+1+2*a))),s},Un.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Se(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},Un.prototype.getPrevPaintRect=function(){return this._prevPaintRect},Un.prototype.animateStyle=function(t){return this.animate("style",t)},Un.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},Un.prototype.attrKV=function(t,e){"style"!==t?Xn.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},Un.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:I(this.style,t),this.dirtyStyle(),this},Un.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},Un.prototype.dirty=function(){this.dirtyStyle()},Un.prototype.styleChanged=function(){return!!(2&this.__dirty)},Un.prototype.styleUpdated=function(){this.__dirty&=-3},Un.prototype.createStyle=function(t){return ht(En,t)},Un.prototype.useStyle=function(t){t[Nn]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},Un.prototype.isStyleObject=function(t){return t[Nn]},Un.prototype._innerSaveToNormal=function(t){Xn.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,Yn)},Un.prototype._applyStateObj=function(t,e,r,i,n,o){Xn.prototype._applyStateObj.call(this,t,e,r,i,n,o);var a,s=!(e&&i);if(e&&e.style?n?i?a=e.style:(a=this._mergeStyle(this.createStyle(),r.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),i?this.style:r.style),this._mergeStyle(a,e.style)):s&&(a=r.style),a)if(n){var h=this.style;if(this.style=this.createStyle(s?{}:h),s)for(var l=F(h),u=0;u<l.length;u++)(p=l[u])in a&&(a[p]=a[p],this.style[p]=h[p]);for(var c=F(a),u=0;u<c.length;u++){var p=c[u];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var f=this.__inHover?jn:Yn,u=0;u<f.length;u++)p=f[u],e&&null!=e[p]?this[p]=e[p]:s&&null!=r[p]&&(this[p]=r[p])},Un.prototype._mergeStates=function(t){for(var e,r=Xn.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var n=t[i];n.style&&(e=e||{},this._mergeStyle(e,n.style))}return e&&(r.style=e),r},Un.prototype._mergeStyle=function(t,e){return I(t,e),t},Un.prototype.getAnimationStyleProps=function(){return Wn},Un.initDefaultProps=((qn=Un.prototype).type="displayable",qn.invisible=!1,qn.z=0,qn.z2=0,qn.zlevel=0,qn.culling=!1,qn.cursor="pointer",qn.rectHover=!1,qn.incremental=!1,qn._rect=null,qn.dirtyRectTolerance=0,void(qn.__dirty=2|Ve)),Un);function Un(t){return Xn.call(this,t)||this}var Gn=new Se(0,0,0,0),Zn=new Se(0,0,0,0);var Kn=Math.min,Qn=Math.max,$n=Math.sin,Jn=Math.cos,to=2*Math.PI,eo=gt(),ro=gt(),io=gt();function no(t,e,r){if(0!==t.length){for(var i=t[0],n=i[0],o=i[0],a=i[1],s=i[1],h=1;h<t.length;h++)i=t[h],n=Kn(n,i[0]),o=Qn(o,i[0]),a=Kn(a,i[1]),s=Qn(s,i[1]);e[0]=n,e[1]=a,r[0]=o,r[1]=s}}function oo(t,e,r,i,n,o){n[0]=Kn(t,r),n[1]=Kn(e,i),o[0]=Qn(t,r),o[1]=Qn(e,i)}var ao=[],so=[];var ho={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},lo=[],uo=[],co=[],po=[],fo=[],yo=[],go=Math.min,vo=Math.max,_o=Math.cos,mo=Math.sin,xo=Math.abs,wo=Math.PI,bo=2*wo,So="undefined"!=typeof Float32Array,ko=[];function To(t){return Math.round(t/wo*1e8)/1e8%2*wo}var Co,Po=(Mo.prototype.increaseVersion=function(){this._version++},Mo.prototype.getVersion=function(){return this._version},Mo.prototype.setScale=function(t,e,r){0<(r=r||0)&&(this._ux=xo(r/Qi/t)||0,this._uy=xo(r/Qi/e)||0)},Mo.prototype.setDPR=function(t){this.dpr=t},Mo.prototype.setContext=function(t){this._ctx=t},Mo.prototype.getContext=function(){return this._ctx},Mo.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},Mo.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},Mo.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(ho.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},Mo.prototype.lineTo=function(t,e){var r,i=xo(t-this._xi),n=xo(e-this._yi),o=i>this._ux||n>this._uy;return this.addData(ho.L,t,e),this._ctx&&o&&this._ctx.lineTo(t,e),o?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=i*i+n*n)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},Mo.prototype.bezierCurveTo=function(t,e,r,i,n,o){return this._drawPendingPt(),this.addData(ho.C,t,e,r,i,n,o),this._ctx&&this._ctx.bezierCurveTo(t,e,r,i,n,o),this._xi=n,this._yi=o,this},Mo.prototype.quadraticCurveTo=function(t,e,r,i){return this._drawPendingPt(),this.addData(ho.Q,t,e,r,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,r,i),this._xi=r,this._yi=i,this},Mo.prototype.arc=function(t,e,r,i,n,o){this._drawPendingPt(),ko[0]=i,ko[1]=n,function(t,e){var r=To(t[0]);r<0&&(r+=bo);var i=r-t[0],n=t[1];n+=i,!e&&bo<=n-r?n=r+bo:e&&bo<=r-n?n=r-bo:!e&&n<r?n=r+(bo-To(r-n)):e&&r<n&&(n=r-(bo-To(n-r))),t[0]=r,t[1]=n}(ko,o),i=ko[0];var a=(n=ko[1])-i;return this.addData(ho.A,t,e,r,r,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,r,i,n,o),this._xi=_o(n)*r+t,this._yi=mo(n)*r+e,this},Mo.prototype.arcTo=function(t,e,r,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,r,i,n),this},Mo.prototype.rect=function(t,e,r,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,r,i),this.addData(ho.R,t,e,r,i),this},Mo.prototype.closePath=function(){this._drawPendingPt(),this.addData(ho.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&t.closePath(),this._xi=e,this._yi=r,this},Mo.prototype.fill=function(t){t&&t.fill(),this.toStatic()},Mo.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},Mo.prototype.len=function(){return this._len},Mo.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!So||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e},Mo.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,r=0,i=this._len,n=0;n<e;n++)r+=t[n].len();for(So&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+r)),n=0;n<e;n++)for(var o=t[n].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},Mo.prototype.addData=function(t,e,r,i,n,o,a,s,h){if(this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var u=0;u<arguments.length;u++)l[this._len++]=arguments[u]}},Mo.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},Mo.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},Mo.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array&&(t.length=this._len,So&&11<this._len&&(this.data=new Float32Array(t))))},Mo.prototype.getBoundingRect=function(){co[0]=co[1]=fo[0]=fo[1]=Number.MAX_VALUE,po[0]=po[1]=yo[0]=yo[1]=-Number.MAX_VALUE;for(var t,e,r,i,n,o,a,s,h,l,u,c,p,f,d=this.data,y=0,g=0,v=0,_=0,m=0;m<this._len;){var x=d[m++],w=1===m;switch(w&&(v=y=d[m],_=g=d[m+1]),x){case ho.M:y=v=d[m++],g=_=d[m++],fo[0]=v,fo[1]=_,yo[0]=v,yo[1]=_;break;case ho.L:oo(y,g,d[m],d[m+1],fo,yo),y=d[m++],g=d[m++];break;case ho.C:!function(t,e,r,i,n,o,a,s,h,l){var u=yr,c=pr,p=u(t,r,n,a,ao);h[0]=1/0,h[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var f=0;f<p;f++){var d=c(t,r,n,a,ao[f]);h[0]=Kn(d,h[0]),l[0]=Qn(d,l[0])}for(p=u(e,i,o,s,so),f=0;f<p;f++){var y=c(e,i,o,s,so[f]);h[1]=Kn(y,h[1]),l[1]=Qn(y,l[1])}h[0]=Kn(t,h[0]),l[0]=Qn(t,l[0]),h[0]=Kn(a,h[0]),l[0]=Qn(a,l[0]),h[1]=Kn(e,h[1]),l[1]=Qn(e,l[1]),h[1]=Kn(s,h[1]),l[1]=Qn(s,l[1])}(y,g,d[m++],d[m++],d[m++],d[m++],d[m],d[m+1],fo,yo),y=d[m++],g=d[m++];break;case ho.Q:t=y,e=g,r=d[m++],i=d[m++],n=d[m],o=d[m+1],a=fo,s=yo,f=p=c=u=l=h=void 0,l=vr,u=Qn(Kn((h=mr)(t,r,n),1),0),c=Qn(Kn(h(e,i,o),1),0),p=l(t,r,n,u),f=l(e,i,o,c),a[0]=Kn(t,n,p),a[1]=Kn(e,o,f),s[0]=Qn(t,n,p),s[1]=Qn(e,o,f),y=d[m++],g=d[m++];break;case ho.A:var b=d[m++],S=d[m++],k=d[m++],T=d[m++],C=d[m++],P=d[m++]+C;m+=1;var M=!d[m++];w&&(v=_o(C)*k+b,_=mo(C)*T+S),function(t,e,r,i,n,o,a,s,h){var l,u=Lt,c=Dt,p=Math.abs(n-o);if(p%to<1e-4&&1e-4<p)return s[0]=t-r,s[1]=e-i,h[0]=t+r,h[1]=e+i;eo[0]=Jn(n)*r+t,eo[1]=$n(n)*i+e,ro[0]=Jn(o)*r+t,ro[1]=$n(o)*i+e,u(s,eo,ro),c(h,eo,ro),(n%=to)<0&&(n+=to),(o%=to)<0&&(o+=to),o<n&&!a?o+=to:n<o&&a&&(n+=to),a&&(l=o,o=n,n=l);for(var f=0;f<o;f+=Math.PI/2)n<f&&(io[0]=Jn(f)*r+t,io[1]=$n(f)*i+e,u(s,io,s),c(h,io,h))}(b,S,k,T,C,P,M,fo,yo),y=_o(P)*k+b,g=mo(P)*T+S;break;case ho.R:oo(v=y=d[m++],_=g=d[m++],v+d[m++],_+d[m++],fo,yo);break;case ho.Z:y=v,g=_}Lt(co,co,fo),Dt(po,po,yo)}return 0===m&&(co[0]=co[1]=po[0]=po[1]=0),new Se(co[0],co[1],po[0]-co[0],po[1]-co[1])},Mo.prototype._calculateLength=function(){var t=this.data,e=this._len,r=this._ux,i=this._uy,n=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,l=0,u=0,c=0;c<e;){var p=t[c++],f=1===c;f&&(a=n=t[c],s=o=t[c+1]);var d=-1;switch(p){case ho.M:n=a=t[c++],o=s=t[c++];break;case ho.L:var y=t[c++],g=(m=t[c++])-o;(xo(A=y-n)>r||xo(g)>i||c===e-1)&&(d=Math.sqrt(A*A+g*g),n=y,o=m);break;case ho.C:var v=t[c++],_=t[c++],y=t[c++],m=t[c++],x=t[c++],w=t[c++],d=function(t,e,r,i,n,o,a,s,h){for(var l=t,u=e,c=0,p=1/h,f=1;f<=h;f++){var d=f*p,y=pr(t,r,n,a,d),g=pr(e,i,o,s,d),v=y-l,_=g-u;c+=Math.sqrt(v*v+_*_),l=y,u=g}return c}(n,o,v,_,y,m,x,w,10),n=x,o=w;break;case ho.Q:d=function(t,e,r,i,n,o,a){for(var s=t,h=e,l=0,u=1/a,c=1;c<=a;c++){var p=c*u,f=vr(t,r,n,p),d=vr(e,i,o,p),y=f-s,g=d-h;l+=Math.sqrt(y*y+g*g),s=f,h=d}return l}(n,o,v=t[c++],_=t[c++],y=t[c++],m=t[c++],10),n=y,o=m;break;case ho.A:var b=t[c++],S=t[c++],k=t[c++],T=t[c++],C=t[c++],P=t[c++],M=P+C;c+=1,t[c++],f&&(a=_o(C)*k+b,s=mo(C)*T+S),d=vo(k,T)*go(bo,Math.abs(P)),n=_o(M)*k+b,o=mo(M)*T+S;break;case ho.R:a=n=t[c++],s=o=t[c++],d=2*t[c++]+2*t[c++];break;case ho.Z:var A=a-n,g=s-o;d=Math.sqrt(A*A+g*g),n=a,o=s}0<=d&&(l+=h[u++]=d)}return this._pathLen=l},Mo.prototype.rebuildPath=function(t,e){var r,i,n,o,a,s,h,l,u,c,p=this.data,f=this._ux,d=this._uy,y=this._len,g=e<1,v=0,_=0,m=0;if(!g||(this._pathSegLen||this._calculateLength(),h=this._pathSegLen,l=e*this._pathLen))t:for(var x=0;x<y;){var w=p[x++],b=1===x;switch(b&&(r=n=p[x],i=o=p[x+1]),w!==ho.L&&0<m&&(t.lineTo(u,c),m=0),w){case ho.M:r=n=p[x++],i=o=p[x++],t.moveTo(n,o);break;case ho.L:a=p[x++],s=p[x++];var S=xo(a-n),k=xo(s-o);if(f<S||d<k){if(g){if(l<v+(j=h[_++])){var T=(l-v)/j;t.lineTo(n*(1-T)+a*T,o*(1-T)+s*T);break t}v+=j}t.lineTo(a,s),n=a,o=s,m=0}else{var C=S*S+k*k;m<C&&(u=a,c=s,m=C)}break;case ho.C:var P=p[x++],M=p[x++],A=p[x++],L=p[x++],D=p[x++],z=p[x++];if(g){if(l<v+(j=h[_++])){gr(n,P,A,D,T=(l-v)/j,lo),gr(o,M,L,z,T,uo),t.bezierCurveTo(lo[1],uo[1],lo[2],uo[2],lo[3],uo[3]);break t}v+=j}t.bezierCurveTo(P,M,A,L,D,z),n=D,o=z;break;case ho.Q:if(P=p[x++],M=p[x++],A=p[x++],L=p[x++],g){if(l<v+(j=h[_++])){xr(n,P,A,T=(l-v)/j,lo),xr(o,M,L,T,uo),t.quadraticCurveTo(lo[1],uo[1],lo[2],uo[2]);break t}v+=j}t.quadraticCurveTo(P,M,A,L),n=A,o=L;break;case ho.A:var I=p[x++],O=p[x++],R=p[x++],F=p[x++],B=p[x++],H=p[x++],N=p[x++],E=!p[x++],W=F<R?R:F,X=.001<xo(R-F),q=B+H,Y=!1;if(g&&(l<v+(j=h[_++])&&(q=B+H*(l-v)/j,Y=!0),v+=j),X&&t.ellipse?t.ellipse(I,O,R,F,N,B,q,E):t.arc(I,O,W,B,q,E),Y)break t;b&&(r=_o(B)*R+I,i=mo(B)*F+O),n=_o(q)*R+I,o=mo(q)*F+O;break;case ho.R:r=n=p[x],i=o=p[x+1],a=p[x++],s=p[x++];var j,V=p[x++],U=p[x++];if(g){if(l<v+(j=h[_++])){var G=l-v;t.moveTo(a,s),t.lineTo(a+go(G,V),s),0<(G-=V)&&t.lineTo(a+V,s+go(G,U)),0<(G-=U)&&t.lineTo(a+vo(V-G,0),s+U),0<(G-=V)&&t.lineTo(a,s+vo(U-G,0));break t}v+=j}t.rect(a,s,V,U);break;case ho.Z:if(g){if(l<v+(j=h[_++])){T=(l-v)/j,t.lineTo(n*(1-T)+r*T,o*(1-T)+i*T);break t}v+=j}t.closePath(),n=r,o=i}}},Mo.prototype.clone=function(){var t=new Mo,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},Mo.CMD=ho,Mo.initDefaultProps=((Co=Mo.prototype)._saveData=!0,Co._ux=0,Co._uy=0,Co._pendingPtDist=0,void(Co._version=0)),Mo);function Mo(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function Ao(t,e,r,i,n,o,a){if(0!==n){var s=n,h=0;if(!(e+s<a&&i+s<a||a<e-s&&a<i-s||t+s<o&&r+s<o||o<t-s&&o<r-s)){if(t===r)return Math.abs(o-t)<=s/2;var l=(h=(e-i)/(t-r))*o-a+(t*i-r*e)/(t-r);return l*l/(h*h+1)<=s/2*s/2}}}function Lo(t,e,r,i,n,o,a,s,h,l,u){if(0!==h){var c=h;if(!(e+c<u&&i+c<u&&o+c<u&&s+c<u||u<e-c&&u<i-c&&u<o-c&&u<s-c||t+c<l&&r+c<l&&n+c<l&&a+c<l||l<t-c&&l<r-c&&l<n-c&&l<a-c))return function(t,e,r,i,n,o,a,s,h,l,u){var c,p,f,d,y,g=.005,v=1/0;sr[0]=h,sr[1]=l;for(var _=0;_<1;_+=.05)hr[0]=pr(t,r,n,a,_),hr[1]=pr(e,i,o,s,_),(d=Pt(sr,hr))<v&&(c=_,v=d);v=1/0;for(var m=0;m<32&&!(g<nr);m++)p=c-g,f=c+g,hr[0]=pr(t,r,n,a,p),hr[1]=pr(e,i,o,s,p),d=Pt(hr,sr),0<=p&&d<v?(c=p,v=d):(lr[0]=pr(t,r,n,a,f),lr[1]=pr(e,i,o,s,f),y=Pt(lr,sr),f<=1&&y<v?(c=f,v=y):g*=.5);return u&&(u[0]=pr(t,r,n,a,c),u[1]=pr(e,i,o,s,c)),rr(v)}(t,e,r,i,n,o,a,s,l,u,null)<=c/2}}function Do(t,e,r,i,n,o,a,s,h){if(0!==a){var l=a;if(!(e+l<h&&i+l<h&&o+l<h||h<e-l&&h<i-l&&h<o-l||t+l<s&&r+l<s&&n+l<s||s<t-l&&s<r-l&&s<n-l))return function(t,e,r,i,n,o,a,s,h){var l,u=.005,c=1/0;sr[0]=a,sr[1]=s;for(var p=0;p<1;p+=.05){hr[0]=vr(t,r,n,p),hr[1]=vr(e,i,o,p),(v=Pt(sr,hr))<c&&(l=p,c=v)}c=1/0;for(var f=0;f<32&&!(u<nr);f++){var d=l-u,y=l+u;hr[0]=vr(t,r,n,d),hr[1]=vr(e,i,o,d);var g,v=Pt(hr,sr);0<=d&&v<c?(l=d,c=v):(lr[0]=vr(t,r,n,y),lr[1]=vr(e,i,o,y),g=Pt(lr,sr),y<=1&&g<c?(l=y,c=g):u*=.5)}return h&&(h[0]=vr(t,r,n,l),h[1]=vr(e,i,o,l)),rr(c)}(t,e,r,i,n,o,s,h,null)<=l/2}}var zo=2*Math.PI;function Io(t){return(t%=zo)<0&&(t+=zo),t}var Oo=2*Math.PI;function Ro(t,e,r,i,n,o){if(e<o&&i<o||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!=a&&0!=a||(s=i<e?.5:-.5);var h=a*(r-t)+t;return h===n?1/0:n<h?s:0}var Fo=Po.CMD,Bo=2*Math.PI,Ho=1e-4;var No=[-1,-1,-1],Eo=[-1,-1];function Wo(t,e,r,i,n,o,a,s,h,l){if(e<l&&i<l&&o<l&&s<l||l<e&&l<i&&l<o&&l<s)return 0;var u=dr(e,i,o,s,l,No);if(0===u)return 0;for(var c,p=0,f=-1,d=void 0,y=void 0,g=0;g<u;g++){var v=No[g],_=0===v||1===v?.5:1;pr(t,r,n,a,v)<h||(f<0&&(f=yr(e,i,o,s,Eo),Eo[1]<Eo[0]&&1<f&&(c=void 0,c=Eo[0],Eo[0]=Eo[1],Eo[1]=c),d=pr(e,i,o,s,Eo[0]),1<f&&(y=pr(e,i,o,s,Eo[1]))),2===f?v<Eo[0]?p+=d<e?_:-_:v<Eo[1]?p+=y<d?_:-_:p+=s<y?_:-_:v<Eo[0]?p+=d<e?_:-_:p+=s<d?_:-_)}return p}function Xo(t,e,r,i,n,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;var h,l,u,c,p,f,d,y,g,v,_,m=(u=No,g=2*((l=i)-(h=e)),v=h-s,_=0,ur(y=h-2*l+o)?cr(g)&&0<=(f=-v/g)&&f<=1&&(u[_++]=f):ur(c=g*g-4*y*v)?0<=(f=-g/(2*y))&&f<=1&&(u[_++]=f):0<c&&(d=(-g-(p=rr(c)))/(2*y),0<=(f=(-g+p)/(2*y))&&f<=1&&(u[_++]=f),0<=d&&d<=1&&(u[_++]=d)),_);if(0===m)return 0;var x=mr(e,i,o);if(0<=x&&x<=1){for(var w=0,b=vr(e,i,o,x),S=0;S<m;S++){var k=0===No[S]||1===No[S]?.5:1;vr(t,r,n,No[S])<a||(No[S]<x?w+=b<e?k:-k:w+=o<b?k:-k)}return w}k=0===No[0]||1===No[0]?.5:1;return vr(t,r,n,No[0])<a?0:o<e?k:-k}function qo(t,e,r,i,n){for(var o,a,s=t.data,h=t.len(),l=0,u=0,c=0,p=0,f=0,d=0;d<h;){var y=s[d++],g=1===d;switch(y===Fo.M&&1<d&&(r||(l+=Ro(u,c,p,f,i,n))),g&&(p=u=s[d],f=c=s[d+1]),y){case Fo.M:u=p=s[d++],c=f=s[d++];break;case Fo.L:if(r){if(Ao(u,c,s[d],s[d+1],e,i,n))return!0}else l+=Ro(u,c,s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case Fo.C:if(r){if(Lo(u,c,s[d++],s[d++],s[d++],s[d++],s[d],s[d+1],e,i,n))return!0}else l+=Wo(u,c,s[d++],s[d++],s[d++],s[d++],s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case Fo.Q:if(r){if(Do(u,c,s[d++],s[d++],s[d],s[d+1],e,i,n))return!0}else l+=Xo(u,c,s[d++],s[d++],s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case Fo.A:var v=s[d++],_=s[d++],m=s[d++],x=s[d++],w=s[d++],b=s[d++];d+=1;var S=!!(1-s[d++]),k=Math.cos(w)*m+v,T=Math.sin(w)*x+_;g?(p=k,f=T):l+=Ro(u,c,k,T,i,n);var C=(i-v)*x/m+v;if(r){if(function(t,e,r,i,n,o,a,s,h){if(0!==a){var l=a;s-=t,h-=e;var u,c=Math.sqrt(s*s+h*h);if(!(r<c-l||c+l<r)){if(Math.abs(i-n)%Oo<1e-4)return 1;(n=o?(u=i,i=Io(n),Io(u)):(i=Io(i),Io(n)))<i&&(n+=Oo);var p=Math.atan2(h,s);return p<0&&(p+=Oo),i<=p&&p<=n||i<=p+Oo&&p+Oo<=n}}}(v,_,x,w,w+b,S,e,C,n))return!0}else l+=function(t,e,r,i,n,o,a,s){if(r<(s-=e)||s<-r)return 0;var h=Math.sqrt(r*r-s*s);No[0]=-h,No[1]=h;var l,u=Math.abs(i-n);if(u<1e-4)return 0;if(Bo-1e-4<=u){n=Bo;var c=o?1:-1;return a>=No[i=0]+t&&a<=No[1]+t?c:0}n<i&&(l=i,i=n,n=l),i<0&&(i+=Bo,n+=Bo);for(var p=0,f=0;f<2;f++){var d,y=No[f];a<y+t&&(c=o?1:-1,(d=Math.atan2(s,y))<0&&(d=Bo+d),(i<=d&&d<=n||i<=d+Bo&&d+Bo<=n)&&(d>Math.PI/2&&d<1.5*Math.PI&&(c=-c),p+=c))}return p}(v,_,x,w,w+b,S,C,n);u=Math.cos(w+b)*m+v,c=Math.sin(w+b)*x+_;break;case Fo.R:p=u=s[d++],f=c=s[d++];if(k=p+s[d++],T=f+s[d++],r){if(Ao(p,f,k,f,e,i,n)||Ao(k,f,k,T,e,i,n)||Ao(k,T,p,T,e,i,n)||Ao(p,T,p,f,e,i,n))return!0}else l+=Ro(k,f,k,T,i,n),l+=Ro(p,T,p,f,i,n);break;case Fo.Z:if(r){if(Ao(u,c,p,f,e,i,n))return!0}else l+=Ro(u,c,p,f,i,n);u=p,c=f}}return r||(o=c,a=f,Math.abs(o-a)<Ho)||(l+=Ro(u,c,p,f,i,n)||0),0!==l}var Yo,jo,Vo=k({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},En),Uo={style:k({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},Wn.style)},Go=un.concat(["invisible","culling","z","z2","zlevel","parent"]),Zo=(yt(Ko,Yo=Vn),Ko.prototype.update=function(){var e=this;Yo.prototype.update.call(this);var t=this.style;if(t.decal){var r=this._decalEl=this._decalEl||new Ko;r.buildPath===Ko.prototype.buildPath&&(r.buildPath=function(t){e.buildPath(t,e.shape)}),r.silent=!0;var i=r.style;for(var n in t)i[n]!==t[n]&&(i[n]=t[n]);i.fill=t.fill?t.decal:null,i.decal=null,i.shadowColor=null,t.strokeFirst&&(i.stroke=null);for(var o=0;o<Go.length;++o)r[Go[o]]=this[Go[o]];r.__dirty|=Ve}else this._decalEl&&(this._decalEl=null)},Ko.prototype.getDecalElement=function(){return this._decalEl},Ko.prototype._init=function(t){var e=F(t);this.shape=this.getDefaultShape();var r=this.getDefaultStyle();r&&this.useStyle(r);for(var i=0;i<e.length;i++){var n=e[i],o=t[n];"style"===n?this.style?I(this.style,o):this.useStyle(o):"shape"===n?I(this.shape,o):Yo.prototype.attrKV.call(this,n,o)}this.style||this.useStyle({})},Ko.prototype.getDefaultStyle=function(){return null},Ko.prototype.getDefaultShape=function(){return{}},Ko.prototype.canBeInsideText=function(){return this.hasFill()},Ko.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(H(t)){var e=Ur(t,0);return.5<e?$i:.2<e?"#eee":Ji}if(t)return Ji}return $i},Ko.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(H(e)){var r=this.__zr;if(!(!r||!r.isDarkMode())==Ur(t,0)<.4)return e}},Ko.prototype.buildPath=function(t,e,r){},Ko.prototype.pathUpdated=function(){this.__dirty&=~Ue},Ko.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},Ko.prototype.createPathProxy=function(){this.path=new Po(!1)},Ko.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},Ko.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},Ko.prototype.getBoundingRect=function(){var t,e,r=this._rect,i=this.style,n=!r;if(n&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&Ue)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),r=e.getBoundingRect()),this._rect=r,this.hasStroke()&&this.path&&0<this.path.len()){var o,a,s,h=this._rectStroke||(this._rectStroke=r.clone());return(this.__dirty||n)&&(h.copy(r),o=i.strokeNoScale?this.getLineScale():1,s=i.lineWidth,this.hasFill()||(a=this.strokeContainThreshold,s=Math.max(s,null==a?4:a)),1e-10<o&&(h.width+=s/o,h.height+=s/o,h.x-=s/o/2,h.y-=s/o/2)),h}return r},Ko.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),n=this.style;if(t=r[0],e=r[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=n.lineWidth,s=n.strokeNoScale?this.getLineScale():1;if(1e-10<s&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),qo(o,a/s,!0,t,e)))return!0}if(this.hasFill())return qo(o,0,!1,t,e)}return!1},Ko.prototype.dirtyShape=function(){this.__dirty|=Ue,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},Ko.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},Ko.prototype.animateShape=function(t){return this.animate("shape",t)},Ko.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},Ko.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):Yo.prototype.attrKV.call(this,t,e)},Ko.prototype.setShape=function(t,e){var r=(r=this.shape)||(this.shape={});return"string"==typeof t?r[t]=e:I(r,t),this.dirtyShape(),this},Ko.prototype.shapeChanged=function(){return!!(this.__dirty&Ue)},Ko.prototype.createStyle=function(t){return ht(Vo,t)},Ko.prototype._innerSaveToNormal=function(t){Yo.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=I({},this.shape))},Ko.prototype._applyStateObj=function(t,e,r,i,n,o){Yo.prototype._applyStateObj.call(this,t,e,r,i,n,o);var a,s=!(e&&i);if(e&&e.shape?n?i?a=e.shape:(a=I({},r.shape),I(a,e.shape)):(a=I({},i?this.shape:r.shape),I(a,e.shape)):s&&(a=r.shape),a)if(n){this.shape=I({},this.shape);for(var h={},l=F(a),u=0;u<l.length;u++){var c=l[u];"object"==typeof a[c]?this.shape[c]=a[c]:h[c]=a[c]}this._transitionState(t,{shape:h},o)}else this.shape=a,this.dirtyShape()},Ko.prototype._mergeStates=function(t){for(var e,r=Yo.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var n=t[i];n.shape&&(e=e||{},this._mergeStyle(e,n.shape))}return e&&(r.shape=e),r},Ko.prototype.getAnimationStyleProps=function(){return Uo},Ko.prototype.isZeroArea=function(){return!1},Ko.extend=function(r){var i,t=(yt(e,i=Ko),e.prototype.getDefaultStyle=function(){return C(r.style)},e.prototype.getDefaultShape=function(){return C(r.shape)},e);function e(t){var e=i.call(this,t)||this;return r.init&&r.init.call(e,t),e}for(var n in r)"function"==typeof r[n]&&(t.prototype[n]=r[n]);return t},Ko.initDefaultProps=((jo=Ko.prototype).type="path",jo.strokeContainThreshold=5,jo.segmentIgnoreThreshold=0,jo.subPixelOptimize=!1,jo.autoBatch=!1,void(jo.__dirty=2|Ve|Ue)),Ko);function Ko(t){return Yo.call(this,t)||this}var Qo=Po.CMD,$o=[[],[],[]],Jo=Math.sqrt,ta=Math.atan2;function ea(t,e){if(e){for(var r,i,n,o,a=t.data,s=t.len(),h=Qo.M,l=Qo.C,u=Qo.L,c=Qo.R,p=Qo.A,f=Qo.Q,d=0,y=0;d<s;){switch(r=a[d++],y=d,i=0,r){case h:case u:i=1;break;case l:i=3;break;case f:i=2;break;case p:var g=e[4],v=e[5],_=Jo(e[0]*e[0]+e[1]*e[1]),m=Jo(e[2]*e[2]+e[3]*e[3]),x=ta(-e[1]/m,e[0]/_);a[d]*=_,a[d++]+=g,a[d]*=m,a[d++]+=v,a[d++]*=_,a[d++]*=m,a[d++]+=x,a[d++]+=x,y=d+=2;break;case c:o[0]=a[d++],o[1]=a[d++],At(o,o,e),a[y++]=o[0],a[y++]=o[1],o[0]+=a[d++],o[1]+=a[d++],At(o,o,e),a[y++]=o[0],a[y++]=o[1]}for(n=0;n<i;n++){var w=$o[n];w[0]=a[d++],w[1]=a[d++],At(w,w,e),a[y++]=w[0],a[y++]=w[1]}}t.increaseVersion()}}var ra=Math.sqrt,ia=Math.sin,na=Math.cos,oa=Math.PI;function aa(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function sa(t,e){return(t[0]*e[0]+t[1]*e[1])/(aa(t)*aa(e))}function ha(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(sa(t,e))}function la(t,e,r,i,n,o,a,s,h,l,u){var c=h*(oa/180),p=na(c)*(t-r)/2+ia(c)*(e-i)/2,f=-1*ia(c)*(t-r)/2+na(c)*(e-i)/2,d=p*p/(a*a)+f*f/(s*s);1<d&&(a*=ra(d),s*=ra(d));var y,g=(n===o?-1:1)*ra((a*a*(s*s)-a*a*(f*f)-s*s*(p*p))/(a*a*(f*f)+s*s*(p*p)))||0,v=g*a*f/s,_=g*-s*p/a,m=(t+r)/2+na(c)*v-ia(c)*_,x=(e+i)/2+ia(c)*v+na(c)*_,w=ha([1,0],[(p-v)/a,(f-_)/s]),b=[(p-v)/a,(f-_)/s],S=[(-1*p-v)/a,(-1*f-_)/s],k=ha(b,S);sa(b,S)<=-1&&(k=oa),1<=sa(b,S)&&(k=0),k<0&&(y=Math.round(k/oa*1e6)/1e6,k=2*oa+y%2*oa),u.addData(l,m,x,a,s,w,k,c,o)}var ua=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,ca=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var pa,fa=(yt(da,pa=Zo),da.prototype.applyTransform=function(t){},da);function da(){return null!==pa&&pa.apply(this,arguments)||this}function ya(t){return null!=t.setData}function ga(t,e){var r=function(t){var e=new Po;if(!t)return e;var r,i=0,n=0,o=i,a=n,s=Po.CMD,h=t.match(ua);if(!h)return e;for(var l=0;l<h.length;l++){for(var u=h[l],c=u.charAt(0),p=void 0,f=u.match(ca)||[],d=f.length,y=0;y<d;y++)f[y]=parseFloat(f[y]);for(var g=0;g<d;){var v=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,S=void 0,k=i,T=n,C=void 0,P=void 0;switch(c){case"l":i+=f[g++],n+=f[g++],p=s.L,e.addData(p,i,n);break;case"L":i=f[g++],n=f[g++],p=s.L,e.addData(p,i,n);break;case"m":i+=f[g++],n+=f[g++],p=s.M,e.addData(p,i,n),o=i,a=n,c="l";break;case"M":i=f[g++],n=f[g++],p=s.M,e.addData(p,i,n),o=i,a=n,c="L";break;case"h":i+=f[g++],p=s.L,e.addData(p,i,n);break;case"H":i=f[g++],p=s.L,e.addData(p,i,n);break;case"v":n+=f[g++],p=s.L,e.addData(p,i,n);break;case"V":n=f[g++],p=s.L,e.addData(p,i,n);break;case"C":p=s.C,e.addData(p,f[g++],f[g++],f[g++],f[g++],f[g++],f[g++]),i=f[g-2],n=f[g-1];break;case"c":p=s.C,e.addData(p,f[g++]+i,f[g++]+n,f[g++]+i,f[g++]+n,f[g++]+i,f[g++]+n),i+=f[g-2],n+=f[g-1];break;case"S":v=i,_=n,C=e.len(),P=e.data,r===s.C&&(v+=i-P[C-4],_+=n-P[C-3]),p=s.C,k=f[g++],T=f[g++],i=f[g++],n=f[g++],e.addData(p,v,_,k,T,i,n);break;case"s":v=i,_=n,C=e.len(),P=e.data,r===s.C&&(v+=i-P[C-4],_+=n-P[C-3]),p=s.C,k=i+f[g++],T=n+f[g++],i+=f[g++],n+=f[g++],e.addData(p,v,_,k,T,i,n);break;case"Q":k=f[g++],T=f[g++],i=f[g++],n=f[g++],p=s.Q,e.addData(p,k,T,i,n);break;case"q":k=f[g++]+i,T=f[g++]+n,i+=f[g++],n+=f[g++],p=s.Q,e.addData(p,k,T,i,n);break;case"T":v=i,_=n,C=e.len(),P=e.data,r===s.Q&&(v+=i-P[C-4],_+=n-P[C-3]),i=f[g++],n=f[g++],p=s.Q,e.addData(p,v,_,i,n);break;case"t":v=i,_=n,C=e.len(),P=e.data,r===s.Q&&(v+=i-P[C-4],_+=n-P[C-3]),i+=f[g++],n+=f[g++],p=s.Q,e.addData(p,v,_,i,n);break;case"A":m=f[g++],x=f[g++],w=f[g++],b=f[g++],S=f[g++],la(k=i,T=n,i=f[g++],n=f[g++],b,S,m,x,w,p=s.A,e);break;case"a":m=f[g++],x=f[g++],w=f[g++],b=f[g++],S=f[g++],la(k=i,T=n,i+=f[g++],n+=f[g++],b,S,m,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,n=a),r=p}return e.toStatic(),e}(t),i=I({},e);return i.buildPath=function(t){var e;ya(t)?(t.setData(r.data),(e=t.getContext())&&t.rebuildPath(e,1)):(e=t,r.rebuildPath(e,1))},i.applyTransform=function(t){ea(r,t),this.dirtyShape()},i}function va(t,e){return new fa(ga(t,e))}function _a(t,e){e=e||{};var r=new Zo;return t.shape&&r.setShape(t.shape),r.setStyle(t.style),e.bakeTransform?ea(r.path,t.getComputedTransform()):e.toLocal?r.setLocalTransform(t.getComputedTransform()):r.copyTransform(t),r.buildPath=t.buildPath,r.applyTransform=r.applyTransform,r.z=t.z,r.z2=t.z2,r.zlevel=t.zlevel,r}var ma=Object.freeze({__proto__:null,createFromString:va,extendFromString:function(t,e){var r,i=ga(t,e);function n(t){var e=r.call(this,t)||this;return e.applyTransform=i.applyTransform,e.buildPath=i.buildPath,e}return yt(n,r=fa),n},mergePath:function(t,e){for(var r=[],i=t.length,n=0;n<i;n++){var o=t[n];r.push(o.getUpdatedPathProxy(!0))}var a=new Zo(e);return a.createPathProxy(),a.buildPath=function(t){var e;ya(t)&&(t.appendPath(r),(e=t.getContext())&&t.rebuildPath(e,1))},a},clonePath:_a}),xa=k({x:0,y:0},En),wa={style:k({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},Wn.style)};var ba,Sa=(yt(ka,ba=Vn),ka.prototype.createStyle=function(t){return ht(xa,t)},ka.prototype._getSize=function(t){var e=this.style,r=e[t];if(null!=r)return r;var i,n=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!n)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?n[t]:n[t]/n[o]*a},ka.prototype.getWidth=function(){return this._getSize("width")},ka.prototype.getHeight=function(){return this._getSize("height")},ka.prototype.getAnimationStyleProps=function(){return wa},ka.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Se(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},ka);function ka(){return null!==ba&&ba.apply(this,arguments)||this}Sa.prototype.type="image";var Ta,Ca=function(){this.cx=0,this.cy=0,this.r=0},Pa=(yt(Ma,Ta=Zo),Ma.prototype.getDefaultShape=function(){return new Ca},Ma.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},Ma);function Ma(t){return Ta.call(this,t)||this}Pa.prototype.type="circle";var Aa=Math.round;function La(t,e,r){if(!e)return t;var i=Aa(2*t);return(i+Aa(e))%2==0?i/2:(i+(r?1:-1))/2}var Da,za=function(){this.x=0,this.y=0,this.width=0,this.height=0},Ia={},Oa=(yt(Ra,Da=Zo),Ra.prototype.getDefaultShape=function(){return new za},Ra.prototype.buildPath=function(t,e){var r,i,n,o,a,s,h,l,u,c,p,f,d,y,g,v,_;this.subPixelOptimize?(i=(r=function(t,e,r){if(e){var i=e.x,n=e.y,o=e.width,a=e.height;t.x=i,t.y=n,t.width=o,t.height=a;var s=r&&r.lineWidth;return s&&(t.x=La(i,s,!0),t.y=La(n,s,!0),t.width=Math.max(La(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(La(n+a,s,!1)-t.y,0===a?0:1)),t}}(Ia,e,this.style)).x,n=r.y,o=r.width,a=r.height,r.r=e.r,e=r):(i=e.x,n=e.y,o=e.width,a=e.height),e.r?(s=t,d=(h=e).x,y=h.y,g=h.width,v=h.height,_=h.r,g<0&&(d+=g,g=-g),v<0&&(y+=v,v=-v),"number"==typeof _?l=u=c=p=_:_ instanceof Array?1===_.length?l=u=c=p=_[0]:2===_.length?(l=c=_[0],u=p=_[1]):3===_.length?(l=_[0],u=p=_[1],c=_[2]):(l=_[0],u=_[1],c=_[2],p=_[3]):l=u=c=p=0,g<l+u&&(l*=g/(f=l+u),u*=g/f),g<c+p&&(c*=g/(f=c+p),p*=g/f),v<u+c&&(u*=v/(f=u+c),c*=v/f),v<l+p&&(l*=v/(f=l+p),p*=v/f),s.moveTo(d+l,y),s.lineTo(d+g-u,y),0!==u&&s.arc(d+g-u,y+u,u,-Math.PI/2,0),s.lineTo(d+g,y+v-c),0!==c&&s.arc(d+g-c,y+v-c,c,0,Math.PI/2),s.lineTo(d+p,y+v),0!==p&&s.arc(d+p,y+v-p,p,Math.PI/2,Math.PI),s.lineTo(d,y+l),0!==l&&s.arc(d+l,y+l,l,Math.PI,1.5*Math.PI)):t.rect(i,n,o,a)},Ra.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},Ra);function Ra(t){return Da.call(this,t)||this}Oa.prototype.type="rect";var Fa,Ba=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},Ha=(yt(Na,Fa=Zo),Na.prototype.getDefaultShape=function(){return new Ba},Na.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.rx,o=e.ry,a=.5522848*n,s=.5522848*o;t.moveTo(r-n,i),t.bezierCurveTo(r-n,i-s,r-a,i-o,r,i-o),t.bezierCurveTo(r+a,i-o,r+n,i-s,r+n,i),t.bezierCurveTo(r+n,i+s,r+a,i+o,r,i+o),t.bezierCurveTo(r-a,i+o,r-n,i+s,r-n,i),t.closePath()},Na);function Na(t){return Fa.call(this,t)||this}Ha.prototype.type="ellipse";var Ea,Wa={},Xa=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},qa=(yt(Ya,Ea=Zo),Ya.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Ya.prototype.getDefaultShape=function(){return new Xa},Ya.prototype.buildPath=function(t,e){var r,i,n,o,a;a=this.subPixelOptimize?(i=(r=function(t,e,r){if(e){var i=e.x1,n=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=n,t.y1=o,t.y2=a;var s=r&&r.lineWidth;return s&&(Aa(2*i)===Aa(2*n)&&(t.x1=t.x2=La(i,s,!0)),Aa(2*o)===Aa(2*a)&&(t.y1=t.y2=La(o,s,!0))),t}}(Wa,e,this.style)).x1,n=r.y1,o=r.x2,r.y2):(i=e.x1,n=e.y1,o=e.x2,e.y2);var s=e.percent;0!==s&&(t.moveTo(i,n),s<1&&(o=i*(1-s)+o*s,a=n*(1-s)+a*s),t.lineTo(o,a))},Ya.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},Ya);function Ya(t){return Ea.call(this,t)||this}function ja(t,e,r){var i=e.smooth,n=e.points;if(n&&2<=n.length){if(i){var o=function(t,e,r,i){var n,o,a,s,h=[],l=[],u=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var p=0,f=t.length;p<f;p++)Lt(a,a,t[p]),Dt(s,s,t[p]);Lt(a,a,i[0]),Dt(s,s,i[1])}for(p=0,f=t.length;p<f;p++){var d=t[p];if(r)n=t[p?p-1:f-1],o=t[(p+1)%f];else{if(0===p||p===f-1){h.push(vt(t[p]));continue}n=t[p-1],o=t[p+1]}mt(l,o,n),bt(l,l,e);var y=kt(d,n),g=kt(d,o),v=y+g;0!==v&&(y/=v,g/=v),bt(u,l,-y),bt(c,l,g);var _=_t([],d,u),m=_t([],d,c);i&&(Dt(_,_,a),Lt(_,_,s),Dt(m,m,a),Lt(m,m,s)),h.push(_),h.push(m)}return r&&h.push(h.shift()),h}(n,i,r,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var a=n.length,s=0;s<(r?a:a-1);s++){var h=o[2*s],l=o[2*s+1],u=n[(s+1)%a];t.bezierCurveTo(h[0],h[1],l[0],l[1],u[0],u[1])}}else{t.moveTo(n[0][0],n[0][1]);for(var s=1,c=n.length;s<c;s++)t.lineTo(n[s][0],n[s][1])}r&&t.closePath()}}qa.prototype.type="line";var Va,Ua=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Ga=(yt(Za,Va=Zo),Za.prototype.getDefaultShape=function(){return new Ua},Za.prototype.buildPath=function(t,e){ja(t,e,!0)},Za);function Za(t){return Va.call(this,t)||this}Ga.prototype.type="polygon";var Ka,Qa=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},$a=(yt(Ja,Ka=Zo),Ja.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Ja.prototype.getDefaultShape=function(){return new Qa},Ja.prototype.buildPath=function(t,e){ja(t,e,!1)},Ja);function Ja(t){return Ka.call(this,t)||this}$a.prototype.type="polyline";var ts=(es.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},es);function es(t){this.colorStops=t||[]}var rs,is=(yt(ns,rs=ts),ns);function ns(t,e,r,i,n,o){var a=rs.call(this,n)||this;return a.x=null==t?0:t,a.y=null==e?0:e,a.x2=null==r?1:r,a.y2=null==i?0:i,a.type="linear",a.global=o||!1,a}var os,as=(yt(ss,os=ts),ss);function ss(t,e,r,i,n){var o=os.call(this,i)||this;return o.x=null==t?.5:t,o.y=null==e?.5:e,o.r=null==r?.5:r,o.type="radial",o.global=n||!1,o}var hs,ls,us=k({strokeFirst:!0,font:E,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Vo),cs=(yt(ps,hs=Vn),ps.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},ps.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},ps.prototype.createStyle=function(t){return ht(us,t)},ps.prototype.setBoundingRect=function(t){this._rect=t},ps.prototype.getBoundingRect=function(){var t,e,r,i=this.style;return this._rect||(null!=(t=i.text)?t+="":t="",(e=function(t,e,r,i){var n=((t||"")+"").split("\n");if(1===n.length)return dn(n[0],e,r,i);for(var o=new Se(0,0,0,0),a=0;a<n.length;a++){var s=dn(n[a],e,r,i);0===a?o.copy(s):o.union(s)}return o}(t,i.font,i.textAlign,i.textBaseline)).x+=i.x||0,e.y+=i.y||0,this.hasStroke()&&(r=i.lineWidth,e.x-=r/2,e.y-=r/2,e.width+=r,e.height+=r),this._rect=e),this._rect},ps.initDefaultProps=void(ps.prototype.dirtyRectTolerance=10),ps);function ps(){return null!==hs&&hs.apply(this,arguments)||this}cs.prototype.type="tspan";var fs={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},ds=F(fs),ys={"alignment-baseline":"textBaseline","stop-color":"stopColor"},gs=F(ys),vs=(_s.prototype.parse=function(t,e){e=e||{};var r=function(t){H(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));var e=t;for(9===e.nodeType&&(e=e.firstChild);"svg"!==e.nodeName.toLowerCase()||1!==e.nodeType;)e=e.nextSibling;return e}(t);this._defsUsePending=[];var i=new zn;this._root=i;var n=[],o=r.getAttribute("viewBox")||"",a=parseFloat(r.getAttribute("width")||e.width),s=parseFloat(r.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(s)&&(s=null),ks(r,i,null,!0,!1);for(var h,l,u,c,p,f,d,y,g,v=r.firstChild;v;)this._parseNode(v,i,n,null,!1,!1),v=v.nextSibling;return function(t,e){for(var r=0;r<e.length;r++){var i=e[r];i[0].style[i[1]]=t[i[2]]}}(this._defs,this._defsUsePending),this._defsUsePending=[],!o||4<=(u=Ms(o)).length&&(h={x:parseFloat(u[0]||0),y:parseFloat(u[1]||0),width:parseFloat(u[2]),height:parseFloat(u[3])}),h&&null!=a&&null!=s&&(d=(f={x:0,y:0,width:a,height:s}).width/(p=h).width,y=f.height/p.height,l={scale:g=Math.min(d,y),x:-(p.x+p.width/2)*g+(f.x+f.width/2),y:-(p.y+p.height/2)*g+(f.y+f.height/2)},e.ignoreViewBox||(c=i,(i=new zn).add(c),c.scaleX=c.scaleY=l.scale,c.x=l.x,c.y=l.y)),e.ignoreRootClip||null==a||null==s||i.setClipPath(new Oa({shape:{x:0,y:0,width:a,height:s}})),{root:i,width:a,height:s,viewBoxRect:h,viewBoxTransform:l,named:n}},_s.prototype._parseNode=function(t,e,r,i,n,o){var a,s,h,l,u,c,p,f=t.nodeName.toLowerCase(),d=i;if("defs"===f&&(n=!0),"text"===f&&(o=!0),"defs"===f||"switch"===f?a=e:(n||(s=ls[f])&&ut(ls,f)&&(a=s.call(this,t,e),(h=t.getAttribute("name"))?(l={name:h,namedFrom:null,svgNodeTagLower:f,el:a},r.push(l),"g"===f&&(d=l)):i&&r.push({name:i.name,namedFrom:i,svgNodeTagLower:f,el:a}),e.add(a)),(u=ms[f])&&ut(ms,f)&&(c=u.call(this,t),(p=t.getAttribute("id"))&&(this._defs[p]=c))),a&&a.isGroup)for(var y=t.firstChild;y;)1===y.nodeType?this._parseNode(y,a,r,d,n,o):3===y.nodeType&&o&&this._parseText(y,a),y=y.nextSibling},_s.prototype._parseText=function(t,e){var r=new cs({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});bs(e,r),ks(t,r,this._defsUsePending,!1,!1),function(t,e){var r,i,n=e.__selfStyle;n&&(r=n.textBaseline,(i=r)&&"auto"!==r&&"baseline"!==r?"before-edge"===r||"text-before-edge"===r?i="top":"after-edge"===r||"text-after-edge"===r?i="bottom":"central"!==r&&"mathematical"!==r||(i="middle"):i="alphabetic",t.style.textBaseline=i);var o,a,s=e.__inheritedStyle;s&&(o=s.textAlign,(a=o)&&("middle"===o&&(a="center"),t.style.textAlign=a))}(r,e);var i=r.style,n=i.fontSize;n&&n<9&&(i.fontSize=9,r.scaleX*=n/9,r.scaleY*=n/9);var o=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=o;var a=r.getBoundingRect();return this._textX+=a.width,e.add(r),r},_s.internalField=void(ls={g:function(t,e){var r=new zn;return bs(e,r),ks(t,r,this._defsUsePending,!1,!1),r},rect:function(t,e){var r=new Oa;return bs(e,r),ks(t,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(t,e){var r=new Pa;return bs(e,r),ks(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),r.silent=!0,r},line:function(t,e){var r=new qa;return bs(e,r),ks(t,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(t,e){var r=new Ha;return bs(e,r),ks(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(t,e){var r,i=t.getAttribute("points");i&&(r=Ss(i));var n=new Ga({shape:{points:r||[]},silent:!0});return bs(e,n),ks(t,n,this._defsUsePending,!1,!1),n},polyline:function(t,e){var r,i=t.getAttribute("points");i&&(r=Ss(i));var n=new $a({shape:{points:r||[]},silent:!0});return bs(e,n),ks(t,n,this._defsUsePending,!1,!1),n},image:function(t,e){var r=new Sa;return bs(e,r),ks(t,r,this._defsUsePending,!1,!1),r.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),r.silent=!0,r},text:function(t,e){var r=t.getAttribute("x")||"0",i=t.getAttribute("y")||"0",n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(n),this._textY=parseFloat(i)+parseFloat(o);var a=new zn;return bs(e,a),ks(t,a,this._defsUsePending,!1,!0),a},tspan:function(t,e){var r=t.getAttribute("x"),i=t.getAttribute("y");null!=r&&(this._textX=parseFloat(r)),null!=i&&(this._textY=parseFloat(i));var n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",a=new zn;return bs(e,a),ks(t,a,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),a},path:function(t,e){var r=va(t.getAttribute("d")||"");return bs(e,r),ks(t,r,this._defsUsePending,!1,!1),r.silent=!0,r}}),_s);function _s(){this._defs={},this._root=null}var ms={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||"0",10),r=parseInt(t.getAttribute("y1")||"0",10),i=parseInt(t.getAttribute("x2")||"10",10),n=parseInt(t.getAttribute("y2")||"0",10),o=new is(e,r,i,n);return xs(t,o),ws(t,o),o},radialgradient:function(t){var e=parseInt(t.getAttribute("cx")||"0",10),r=parseInt(t.getAttribute("cy")||"0",10),i=parseInt(t.getAttribute("r")||"0",10),n=new as(e,r,i);return xs(t,n),ws(t,n),n}};function xs(t,e){"userSpaceOnUse"===t.getAttribute("gradientUnits")&&(e.global=!0)}function ws(t,e){for(var r,i,n,o,a=t.firstChild;a;){1===a.nodeType&&"stop"===a.nodeName.toLocaleLowerCase()&&(i=void 0,i=(r=a.getAttribute("offset"))&&0<r.indexOf("%")?parseInt(r,10)/100:r?parseFloat(r):0,zs(a,n={},n),o=n.stopColor||a.getAttribute("stop-color")||"#000000",e.colorStops.push({offset:i,color:o})),a=a.nextSibling}}function bs(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),k(e.__inheritedStyle,t.__inheritedStyle))}function Ss(t){for(var e=Ms(t),r=[],i=0;i<e.length;i+=2){var n=parseFloat(e[i]),o=parseFloat(e[i+1]);r.push([n,o])}return r}function ks(t,e,r,i,n){var o=e,a=o.__inheritedStyle=o.__inheritedStyle||{},s={};1===t.nodeType&&(function(t,e){var r=t.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=[],n=null;r.replace(As,function(t,e,r){return i.push(e,r),""});for(var o=i.length-1;0<o;o-=2){var a=i[o],s=i[o-1],h=Ms(a);switch(n=n||ne(),s){case"translate":he(n,n,[parseFloat(h[0]),parseFloat(h[1]||"0")]);break;case"scale":ue(n,n,[parseFloat(h[0]),parseFloat(h[1]||h[0])]);break;case"rotate":le(n,n,-parseFloat(h[0])*Ls);break;case"skewX":var l=Math.tan(parseFloat(h[0])*Ls);se(n,[1,0,l,1,0,0],n);break;case"skewY":var u=Math.tan(parseFloat(h[0])*Ls);se(n,[1,u,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(h[0]),n[1]=parseFloat(h[1]),n[2]=parseFloat(h[2]),n[3]=parseFloat(h[3]),n[4]=parseFloat(h[4]),n[5]=parseFloat(h[5])}}e.setLocalTransform(n)}}(t,e),zs(t,a,s),i||function(t,e,r){for(var i=0;i<ds.length;i++){var n=ds[i];null!=(o=t.getAttribute(n))&&(e[fs[n]]=o)}for(i=0;i<gs.length;i++){var o,n=gs[i];null!=(o=t.getAttribute(n))&&(r[ys[n]]=o)}}(t,a,s)),o.style=o.style||{},null!=a.fill&&(o.style.fill=Cs(o,"fill",a.fill,r)),null!=a.stroke&&(o.style.stroke=Cs(o,"stroke",a.stroke,r)),O(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){null!=a[t]&&(o.style[t]=parseFloat(a[t]))}),O(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(t){null!=a[t]&&(o.style[t]=a[t])}),n&&(o.__selfStyle=s),a.lineDash&&(o.style.lineDash=L(Ms(a.lineDash),function(t){return parseFloat(t)})),"hidden"!==a.visibility&&"collapse"!==a.visibility||(o.invisible=!0),"none"===a.display&&(o.ignore=!0)}var Ts=/^url\(\s*#(.*?)\)/;function Cs(t,e,r,i){var n=r&&r.match(Ts);if(!n)return"none"===r&&(r=null),r;var o=tt(n[1]);i.push([t,e,o])}var Ps=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Ms(t){return t.match(Ps)||[]}var As=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Ls=Math.PI/180;var Ds=/([^\s:;]+)\s*:\s*([^:;]+)/g;function zs(t,e,r){var i,n=t.getAttribute("style");if(n)for(Ds.lastIndex=0;null!=(i=Ds.exec(n));){var o=i[1],a=ut(fs,o)?fs[o]:null;a&&(e[a]=i[2]);var s=ut(ys,o)?ys[o]:null;s&&(r[s]=i[2])}}var Is=Math.PI,Os=2*Is,Rs=Math.sin,Fs=Math.cos,Bs=Math.acos,Hs=Math.atan2,Ns=Math.abs,Es=Math.sqrt,Ws=Math.max,Xs=Math.min,qs=1e-4;function Ys(t,e,r,i,n,o,a){var s=t-r,h=e-i,l=(a?o:-o)/Es(s*s+h*h),u=l*h,c=-l*s,p=t+u,f=e+c,d=r+u,y=i+c,g=(p+d)/2,v=(f+y)/2,_=d-p,m=y-f,x=_*_+m*m,w=n-o,b=p*y-d*f,S=(m<0?-1:1)*Es(Ws(0,w*w*x-b*b)),k=(b*m-_*S)/x,T=(-b*_-m*S)/x,C=(b*m+_*S)/x,P=(-b*_+m*S)/x,M=k-g,A=T-v,L=C-g,D=P-v;return L*L+D*D<M*M+A*A&&(k=C,T=P),{cx:k,cy:T,x0:-u,y0:-c,x1:k*(n/w-1),y1:T*(n/w-1)}}function js(t,e){var r,i,n,o,a,s,h,l,u,c,p,f,d,y,g,v,_,m,x,w,b,S,k,T,C,P,M,A,L,D,z,I,O,R,F,B,H,N,E,W,X,q,Y,j=Ws(e.r,0),V=Ws(e.r0||0,0),U=0<j;(U||0<V)&&(U||(j=V,V=0),j<V&&(i=j,j=V,V=i),n=e.startAngle,o=e.endAngle,isNaN(n)||isNaN(o)||(a=e.cx,s=e.cy,h=!!e.clockwise,l=Ns(o-n),qs<(u=Os<l&&l%Os)&&(l=u),qs<j?Os-qs<l?(t.moveTo(a+j*Fs(n),s+j*Rs(n)),t.arc(a,s,j,n,o,!h),qs<V&&(t.moveTo(a+V*Fs(o),s+V*Rs(o)),t.arc(a,s,V,o,n,h))):(F=R=O=I=z=D=g=y=E=N=H=B=d=f=p=c=void 0,v=j*Fs(n),_=j*Rs(n),m=V*Fs(o),x=V*Rs(o),(w=qs<l)&&((b=e.cornerRadius)&&(c=(r=function(t){var e;if(G(t)){var r=t.length;if(!r)return t;e=1===r?[t[0],t[0],0,0]:2===r?[t[0],t[0],t[1],t[1]]:3===r?t.concat(t[2]):t}else e=[t,t,t,t];return e}(b))[0],p=r[1],f=r[2],d=r[3]),S=Ns(j-V)/2,B=Xs(S,f),H=Xs(S,d),N=Xs(S,c),E=Xs(S,p),D=y=Ws(B,H),z=g=Ws(N,E),(qs<y||qs<g)&&(I=j*Fs(o),O=j*Rs(o),R=V*Fs(n),F=V*Rs(n),l<Is&&((k=function(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,c=s-o,p=c*h-u*l;if(!(p*p<qs))return[t+(p=(u*(e-o)-c*(t-n))/p)*h,e+p*l]}(v,_,R,F,I,O,m,x))&&(T=v-k[0],C=_-k[1],P=I-k[0],M=O-k[1],A=1/Rs(Bs((T*P+C*M)/(Es(T*T+C*C)*Es(P*P+M*M)))/2),L=Es(k[0]*k[0]+k[1]*k[1]),D=Xs(y,(j-L)/(1+A)),z=Xs(g,(V-L)/(A-1)))))),w?qs<D?(W=Xs(f,D),X=Xs(d,D),q=Ys(R,F,v,_,j,W,h),Y=Ys(I,O,m,x,j,X,h),t.moveTo(a+q.cx+q.x0,s+q.cy+q.y0),D<y&&W===X?t.arc(a+q.cx,s+q.cy,D,Hs(q.y0,q.x0),Hs(Y.y0,Y.x0),!h):(0<W&&t.arc(a+q.cx,s+q.cy,W,Hs(q.y0,q.x0),Hs(q.y1,q.x1),!h),t.arc(a,s,j,Hs(q.cy+q.y1,q.cx+q.x1),Hs(Y.cy+Y.y1,Y.cx+Y.x1),!h),0<X&&t.arc(a+Y.cx,s+Y.cy,X,Hs(Y.y1,Y.x1),Hs(Y.y0,Y.x0),!h))):(t.moveTo(a+v,s+_),t.arc(a,s,j,n,o,!h)):t.moveTo(a+v,s+_),qs<V&&w?qs<z?(W=Xs(c,z),q=Ys(m,x,I,O,V,-(X=Xs(p,z)),h),Y=Ys(v,_,R,F,V,-W,h),t.lineTo(a+q.cx+q.x0,s+q.cy+q.y0),z<g&&W===X?t.arc(a+q.cx,s+q.cy,z,Hs(q.y0,q.x0),Hs(Y.y0,Y.x0),!h):(0<X&&t.arc(a+q.cx,s+q.cy,X,Hs(q.y0,q.x0),Hs(q.y1,q.x1),!h),t.arc(a,s,V,Hs(q.cy+q.y1,q.cx+q.x1),Hs(Y.cy+Y.y1,Y.cx+Y.x1),h),0<W&&t.arc(a+Y.cx,s+Y.cy,W,Hs(Y.y1,Y.x1),Hs(Y.y0,Y.x0),!h))):(t.lineTo(a+m,s+x),t.arc(a,s,V,o,n,h)):t.lineTo(a+m,s+x)):t.moveTo(a,s),t.closePath()))}var Vs,Us=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Gs=(yt(Zs,Vs=Zo),Zs.prototype.getDefaultShape=function(){return new Us},Zs.prototype.buildPath=function(t,e){js(t,e)},Zs.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Zs);function Zs(t){return Vs.call(this,t)||this}Gs.prototype.type="sector";var Ks=Po.CMD;function Qs(t,e){return Math.abs(t-e)<1e-5}function $s(t){var n,e,r,i=t.data,o=t.len(),a=[],s=0,h=0,l=0,u=0;function c(t,e){n&&2<n.length&&a.push(n),n=[t,e]}function p(t,e,r,i){Qs(t,r)&&Qs(e,i)||n.push(t,e,r,i,r,i)}for(var f,d,y,g,v,_,m,x,w,b,S,k,T,C,P,M,A,L,D,z=0;z<o;){var I=i[z++],O=1===z;switch(O&&(l=s=i[z],u=h=i[z+1],I!==Ks.L&&I!==Ks.C&&I!==Ks.Q||(n=[l,u])),I){case Ks.M:s=l=i[z++],h=u=i[z++],c(l,u);break;case Ks.L:p(s,h,X=i[z++],q=i[z++]),s=X,h=q;break;case Ks.C:n.push(i[z++],i[z++],i[z++],i[z++],s=i[z++],h=i[z++]);break;case Ks.Q:X=i[z++],q=i[z++],e=i[z++],r=i[z++],n.push(s+2/3*(X-s),h+2/3*(q-h),e+2/3*(X-e),r+2/3*(q-r),e,r),s=e,h=r;break;case Ks.A:var R=i[z++],F=i[z++],B=i[z++],H=i[z++],N=i[z++],E=i[z++]+N;z+=1;var W=!i[z++],X=Math.cos(N)*B+R,q=Math.sin(N)*H+F;O?c(l=X,u=q):p(s,h,X,q),s=Math.cos(E)*B+R,h=Math.sin(E)*H+F;for(var Y=(W?-1:1)*Math.PI/2,j=N;W?E<j:j<E;j+=Y){var V=W?Math.max(j+Y,E):Math.min(j+Y,E);f=j,d=V,y=R,g=F,v=B,_=H,D=L=A=M=P=C=T=k=S=b=w=x=m=void 0,m=Math.abs(d-f),x=4*Math.tan(m/4)/3,w=d<f?-1:1,b=Math.cos(f),S=Math.sin(f),k=Math.cos(d),T=Math.sin(d),C=b*v+y,P=S*_+g,M=k*v+y,A=T*_+g,L=v*x*w,D=_*x*w,n.push(C-L*S,P+D*b,M+L*T,A-D*k,M,A)}break;case Ks.R:l=s=i[z++],u=h=i[z++],X=l+i[z++],q=u+i[z++],c(X,u),p(X,u,X,q),p(X,q,l,q),p(l,q,l,u),p(l,u,X,u);break;case Ks.Z:n&&p(s,h,l,u),s=l,h=u}}return n&&2<n.length&&a.push(n),a}function Js(t,e){var r=$s(t),i=[];e=e||1;for(var n=0;n<r.length;n++){var o=r[n],a=[],s=o[0],h=o[1];a.push(s,h);for(var l=2;l<o.length;){var u=o[l++],c=o[l++],p=o[l++],f=o[l++],d=o[l++],y=o[l++];!function t(e,r,i,n,o,a,s,h,l,u){var c,p,f,d,y,g,v,_,m,x,w,b,S,k,T;Qs(e,i)&&Qs(r,n)&&Qs(o,s)&&Qs(a,h)?l.push(s,h):(p=(c=2/u)*c,f=s-e,d=h-r,f/=y=Math.sqrt(f*f+d*d),d/=y,w=(_=o-s)*_+(m=a-h)*m,(x=(g=i-e)*g+(v=n-r)*v)<p&&w<p?l.push(s,h):(S=-f*_-d*m,x-(b=f*g+d*v)*b<p&&0<=b&&w-S*S<p&&0<=S?l.push(s,h):(T=[],gr(e,i,o,s,.5,k=[]),gr(r,n,a,h,.5,T),t(k[0],T[0],k[1],T[1],k[2],T[2],k[3],T[3],l,u),t(k[4],T[4],k[5],T[5],k[6],T[6],k[7],T[7],l,u))))}(s,h,u,c,p,f,d,y,a,e),s=d,h=y}i.push(a)}return i}function th(t,e,r){var i=t[e],n=t[1-e],o=Math.abs(i/n),a=Math.ceil(Math.sqrt(o*r)),s=Math.floor(r/a);0===s&&(s=1,a=r);for(var h=[],l=0;l<a;l++)h.push(s);var u=r-a*s;if(0<u)for(l=0;l<u;l++)h[l%a]+=1;return h}function eh(t,e,r){for(var i=t.r0,n=t.r,o=t.startAngle,a=t.endAngle,s=Math.abs(a-o),h=s*n,l=n-i,u=h>Math.abs(l),c=th([h,l],u?0:1,e),p=(u?s:l)/c.length,f=0;f<c.length;f++)for(var d=(u?l:s)/c[f],y=0;y<c[f];y++){var g={};u?(g.startAngle=o+p*f,g.endAngle=o+p*(f+1),g.r0=i+d*y,g.r=i+d*(y+1)):(g.startAngle=o+d*y,g.endAngle=o+d*(y+1),g.r0=i+p*f,g.r=i+p*(f+1)),g.clockwise=t.clockwise,g.cx=t.cx,g.cy=t.cy,r.push(g)}}function rh(t,e){var r=t[t.length-1];r&&r[0]===e[0]&&r[1]===e[1]||t.push(e)}function ih(t,e,r){for(var i=t.length,n=[],o=0;o<i;o++){var a=t[o],s=t[(o+1)%i],h=function(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,c=s-o,p=u*l-h*c;if(Math.abs(p)<1e-6)return null;var f=((t-n)*c-u*(e-o))/p;return f<0||1<f?null:new fe(f*h+t,f*l+e)}(a[0],a[1],s[0],s[1],e.x,e.y,r.x,r.y);h&&n.push({projPt:function(t,e,r){var i=new fe;fe.sub(i,r,e),i.normalize();var n=new fe;return fe.sub(n,t,e),n.dot(i)}(h,e,r),pt:h,idx:o})}if(n.length<2)return[{points:t},{points:t}];n.sort(function(t,e){return t.projPt-e.projPt});var l,u=n[0],c=n[n.length-1];c.idx<u.idx&&(l=u,u=c,c=l);for(var p=[u.pt.x,u.pt.y],f=[c.pt.x,c.pt.y],d=[p],y=[f],o=u.idx+1;o<=c.idx;o++)rh(d,t[o].slice());rh(d,f),rh(d,p);for(o=c.idx+1;o<=u.idx+i;o++)rh(y,t[o%i].slice());return rh(y,p),rh(y,f),[{points:d},{points:y}]}function nh(t){var e=t.points,r=[],i=[];no(e,r,i);var n=new Se(r[0],r[1],i[0]-r[0],i[1]-r[1]),o=n.width,a=n.height,s=n.x,h=n.y,l=new fe,u=new fe;return a<o?(l.x=u.x=s+o/2,l.y=h,u.y=h+a):(l.y=u.y=h+a/2,l.x=s,u.x=s+o),ih(e,l,u)}function oh(t,e,r,i){var n,o;return 1===r?i.push(e):(n=Math.floor(r/2),o=t(e),oh(t,o[0],n,i),oh(t,o[1],r-n,i)),i}function ah(t,e){var r,i=[],n=t.shape;switch(t.type){case"rect":!function(t,e,r){for(var i=t.width,n=t.height,o=n<i,a=th([i,n],o?0:1,e),s=o?"width":"height",h=o?"height":"width",l=o?"x":"y",u=o?"y":"x",c=t[s]/a.length,p=0;p<a.length;p++)for(var f=t[h]/a[p],d=0;d<a[p];d++){var y={};y[l]=p*c,y[u]=d*f,y[s]=c,y[h]=f,y.x+=t.x,y.y+=t.y,r.push(y)}}(n,e,i),r=Oa;break;case"sector":eh(n,e,i),r=Gs;break;case"circle":eh({r0:0,r:n.r,startAngle:0,endAngle:2*Math.PI,cx:n.cx,cy:n.cy},e,i),r=Gs;break;default:var o=t.getComputedTransform(),a=o?Math.sqrt(Math.max(o[0]*o[0]+o[1]*o[1],o[2]*o[2]+o[3]*o[3])):1,s=L(Js(t.getUpdatedPathProxy(),a),function(t){for(var e=[],r=0;r<t.length;)e.push([t[r++],t[r++]]);return e}),h=s.length;if(0===h)oh(nh,{points:s[0]},e,i);else if(h===e)for(var l=0;l<h;l++)i.push({points:s[l]});else{var u=0,c=L(s,function(t){var e=[],r=[];no(t,e,r);var i=(r[1]-e[1])*(r[0]-e[0]);return u+=i,{poly:t,area:i}});c.sort(function(t,e){return e.area-t.area});for(var p=e,l=0;l<h;l++){var f=c[l];if(p<=0)break;var d=l===h-1?p:Math.ceil(f.area/u*e);d<0||(oh(nh,{points:f.poly},d,i),p-=d)}}r=Ga}if(!r)return function(t,e){for(var r=[],i=0;i<e;i++)r.push(_a(t));return r}(t,e);for(var y,g,v=[],l=0;l<i.length;l++){var _=new r;_.setShape(i[l]),y=t,(g=_).setStyle(y.style),g.z=y.z,g.z2=y.z2,g.zlevel=y.zlevel,v.push(_)}return v}function sh(t,e){for(var r=t.length,i=t[r-2],n=t[r-1],o=[],a=0;a<e.length;)o[a++]=i,o[a++]=n;return o}function hh(t,e){for(var r,i,n,o=[],a=[],s=0;s<Math.max(t.length,e.length);s++){var h=t[s],l=e[s],u=void 0,c=void 0;h?l?(i=u=(r=function(t,e){var r=t.length,i=e.length;if(r===i)return[t,e];for(var n=[],o=[],a=r<i?t:e,s=Math.min(r,i),h=Math.abs(i-r)/6,l=(s-2)/6,u=Math.ceil(h/l)+1,c=[a[0],a[1]],p=h,f=2;f<s;){var d=a[f-2],y=a[f-1],g=a[f++],v=a[f++],_=a[f++],m=a[f++],x=a[f++],w=a[f++];if(p<=0)c.push(g,v,_,m,x,w);else{for(var b=Math.min(p,u-1)+1,S=1;S<=b;S++){var k=S/b;gr(d,g,_,x,k,n),gr(y,v,m,w,k,o),d=n[3],y=o[3],c.push(n[1],o[1],n[2],o[2],d,y),g=n[5],v=o[5],_=n[6],m=o[6]}p-=b-1}}return a===t?[c,e]:[t,c]}(h,l))[0],n=c=r[1]):(c=sh(n||h,h),u=h):(u=sh(i||l,l),c=l),o.push(u),a.push(c)}return[o,a]}function lh(t){for(var e=0,r=0,i=0,n=t.length,o=0,a=n-2;o<n;a=o,o+=2){var s=t[a],h=t[a+1],l=t[o],u=t[o+1],c=s*u-l*h;e+=c,r+=(s+l)*c,i+=(h+u)*c}return 0===e?[t[0]||0,t[1]||0]:[r/e/3,i/e/3,e]}function uh(t,e,r,i){for(var n,o=[],a=0;a<t.length;a++){var s=t[a],h=e[a],l=lh(s),u=lh(h);null==n&&(n=l[2]<0!=u[2]<0);var c=[],p=[],f=0,d=1/0,y=[],g=s.length;n&&(s=function(t){for(var e=[],r=t.length,i=0;i<r;i+=2)e[i]=t[r-i-2],e[i+1]=t[r-i-1];return e}(s));for(var v=6*function(t,e,r,i){for(var n=(t.length-2)/6,o=1/0,a=0,s=t.length,h=s-2,l=0;l<n;l++){for(var u=6*l,c=0,p=0;p<s;p+=2){var f=0===p?u:(u+p-2)%h+2,d=t[f]-r[0],y=t[1+f]-r[1],g=e[p]-i[0]-d,v=e[p+1]-i[1]-y;c+=g*g+v*v}c<o&&(o=c,a=l)}return a}(s,h,l,u),_=g-2,m=0;m<_;m+=2){var x=(v+m)%_+2;c[m+2]=s[x]-l[0],c[m+3]=s[1+x]-l[1]}if(c[0]=s[v]-l[0],c[1]=s[1+v]-l[1],0<r)for(var w=i/r,b=-i/2;b<=i/2;b+=w){for(var S=Math.sin(b),k=Math.cos(b),T=0,m=0;m<s.length;m+=2){var C=c[m],P=c[m+1],M=h[m]-u[0],A=h[m+1]-u[1],L=M*k-A*S,D=M*S+A*k,z=(y[m]=L)-C,I=(y[m+1]=D)-P;T+=z*z+I*I}if(T<d){d=T,f=b;for(var O=0;O<y.length;O++)p[O]=y[O]}}else for(var R=0;R<g;R+=2)p[R]=h[R]-u[0],p[R+1]=h[R+1]-u[1];o.push({from:c,to:p,fromCp:l,toCp:u,rotation:-f})}return o}function ch(t){return t.__isCombineMorphing}var ph="__mOriginal_";function fh(t,e,r){var i=ph+e,n=t[i]||t[e];t[i]||(t[i]=t[e]);var o=r.replace,a=r.after,s=r.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=o?o.apply(this,e):n.apply(this,e),a&&a.apply(this,e),t}}function dh(t,e){var r=ph+e;t[r]&&(t[e]=t[r],t[r]=null)}function yh(t,e){for(var r=0;r<t.length;r++)for(var i=t[r],n=0;n<i.length;){var o=i[n],a=i[n+1];i[n++]=e[0]*o+e[2]*a+e[4],i[n++]=e[1]*o+e[3]*a+e[5]}}function gh(t,C){var e=t.getUpdatedPathProxy(),r=C.getUpdatedPathProxy(),i=hh($s(e),$s(r)),n=i[0],o=i[1],a=t.getComputedTransform(),s=C.getComputedTransform();a&&yh(n,a),s&&yh(o,s),fh(C,"updateTransform",{replace:function(){this.transform=null}}),C.transform=null;var P=uh(n,o,10,Math.PI),M=[];fh(C,"buildPath",{replace:function(t){for(var e=C.__morphT,r=1-e,i=[],n=0;n<P.length;n++){var o=P[n],a=o.from,s=o.to,h=o.rotation*e,l=o.fromCp,u=o.toCp,c=Math.sin(h),p=Math.cos(h);Mt(i,l,u,e);for(var f=0;f<a.length;f+=2){var d=a[f],y=a[f+1],g=d*r+(x=s[f])*e,v=y*r+(w=s[f+1])*e;M[f]=g*p-v*c+i[0],M[f+1]=g*c+v*p+i[1]}var _=M[0],m=M[1];t.moveTo(_,m);for(f=2;f<a.length;){var x=M[f++],w=M[f++],b=M[f++],S=M[f++],k=M[f++],T=M[f++];_===x&&m===w&&b===k&&S===T?t.lineTo(k,T):t.bezierCurveTo(x,w,b,S,k,T),_=k,m=T}}}})}function vh(t,e,r){if(!t||!e)return e;var i=r.done,n=r.during;return gh(t,e),e.__morphT=0,e.animateTo({__morphT:1},k({during:function(t){e.dirtyShape(),n&&n(t)},done:function(){dh(e,"buildPath"),dh(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape(),i&&i()}},r)),e}function _h(r){var o=1/0,a=1/0,s=-1/0,h=-1/0,t=L(r,function(t){var e=t.getBoundingRect(),r=t.getComputedTransform(),i=e.x+e.width/2+(r?r[4]:0),n=e.y+e.height/2+(r?r[5]:0);return o=Math.min(i,o),a=Math.min(n,a),s=Math.max(i,s),h=Math.max(n,h),[i,n]});return L(t,function(t,e){return{cp:t,z:function(t,e,r,i,n,o){t=n===r?0:Math.round(32767*(t-r)/(n-r)),e=o===i?0:Math.round(32767*(e-i)/(o-i));for(var a,s=0,h=32768;0<h;h/=2){var l=0,u=0;0<(t&h)&&(l=1),0<(e&h)&&(u=1),s+=h*h*(3*l^u),0===u&&(1===l&&(t=h-1-t,e=h-1-e),a=t,t=e,e=a)}return s}(t[0],t[1],o,a,s,h),path:r[e]}}).sort(function(t,e){return t.z-e.z}).map(function(t){return t.path})}function mh(t){return ah(t.path,t.count)}function xh(){return{fromIndividuals:[],toIndividuals:[],count:0}}var wh,bh=Object.freeze({__proto__:null,alignBezierCurves:hh,centroid:lh,isCombineMorphing:ch,isMorphing:function(t){return 0<=t.__morphT},morphPath:vh,combineMorph:function(e,i,t){var n=[];!function t(e){for(var r=0;r<e.length;r++){var i=e[r];ch(i)?t(i.childrenRef()):i instanceof Zo&&n.push(i)}}(e);var r=n.length;if(!r)return xh();var o=(t.dividePath||mh)({path:i,count:r});if(o.length!==r)return console.error("Invalid morphing: unmatched splitted path"),xh();n=_h(n),o=_h(o);for(var a=t.done,s=t.during,h=t.individualDelay,l=new hn,u=0;u<r;u++){var c=n[u],p=o[u];p.parent=i,p.copyTransform(l),h||gh(c,p)}function f(t){for(var e=0;e<o.length;e++)o[e].addSelfToZr(t)}function d(){i.__isCombineMorphing=!1,i.__morphT=-1,i.childrenRef=null,dh(i,"addSelfToZr"),dh(i,"removeSelfFromZr")}i.__isCombineMorphing=!0,i.childrenRef=function(){return o},fh(i,"addSelfToZr",{after:function(t){f(t)}}),fh(i,"removeSelfFromZr",{after:function(t){for(var e=0;e<o.length;e++)o[e].removeSelfFromZr(t)}});var y=o.length;if(h)for(var g=y,v=function(){0===--g&&(d(),a&&a())},u=0;u<y;u++){var _=h?k({delay:(t.delay||0)+h(u,y,n[u],o[u]),done:v},t):t;vh(n[u],o[u],_)}else i.__morphT=0,i.animateTo({__morphT:1},k({during:function(t){for(var e=0;e<y;e++){var r=o[e];r.__morphT=i.__morphT,r.dirtyShape()}s&&s(t)},done:function(){d();for(var t=0;t<e.length;t++)dh(e[t],"updateTransform");a&&a()}},t));return i.__zr&&f(i.__zr),{fromIndividuals:n,toIndividuals:o,count:y}},separateMorph:function(t,e,r){var i=e.length,n=[],o=r.dividePath||mh;if(ch(t)){!function t(e){for(var r=0;r<e.length;r++){var i=e[r];ch(i)?t(i.childrenRef()):i instanceof Zo&&n.push(i)}}(t.childrenRef());var a=n.length;if(a<i)for(var s=0,h=a;h<i;h++)n.push(_a(n[s++%a]));n.length=i}else{n=o({path:t,count:i});for(var l=t.getComputedTransform(),h=0;h<n.length;h++)n[h].setLocalTransform(l);if(n.length!==i)return console.error("Invalid morphing: unmatched splitted path"),xh()}n=_h(n),e=_h(e);for(var u=r.individualDelay,h=0;h<i;h++){var c=u?k({delay:(r.delay||0)+u(h,i,n[h],e[h])},r):r;vh(n[h],e[h],c)}return{fromIndividuals:n,toIndividuals:e,count:e.length}},defaultDividePath:ah}),Sh=(yt(kh,wh=Zo),kh.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),r=0;r<t.length;r++)e=e||t[r].shapeChanged();e&&this.dirtyShape()},kh.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1],t[r].segmentIgnoreThreshold)},kh.prototype.buildPath=function(t,e){for(var r=e.paths||[],i=0;i<r.length;i++)r[i].buildPath(t,r[i].shape,!0)},kh.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},kh.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),Zo.prototype.getBoundingRect.call(this)},kh);function kh(){var t=null!==wh&&wh.apply(this,arguments)||this;return t.type="compound",t}var Th,Ch=[],Ph=(yt(Mh,Th=Vn),Mh.prototype.traverse=function(t,e){t.call(e,this)},Mh.prototype.useStyle=function(){this.style={}},Mh.prototype.getCursor=function(){return this._cursor},Mh.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},Mh.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},Mh.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},Mh.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},Mh.prototype.addDisplayables=function(t,e){e=e||!1;for(var r=0;r<t.length;r++)this.addDisplayable(t[r],e)},Mh.prototype.getDisplayables=function(){return this._displayables},Mh.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},Mh.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Mh.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},Mh.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Se(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var r=this._displayables[e],i=r.getBoundingRect().clone();r.needLocalTransform()&&i.applyTransform(r.getLocalTransform(Ch)),t.union(i)}this._rect=t}return this._rect},Mh.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(r[0],r[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},Mh);function Mh(){var t=null!==Th&&Th.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Ah=new Mr(50);function Lh(t,e,r,i,n){if(t){if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!r)return e;var o=Ah.get(t),a={hostEl:r,cb:i,cbPayload:n};return o?zh(e=o.image)||o.pending.push(a):((e=f.loadImage(t,Dh,Dh)).__zrImageSrc=t,Ah.put(t,e.__cachedImgObj={image:e,pending:[a]})),e}return e}function Dh(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}function zh(t){return t&&t.width&&t.height}var Ih=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Oh(t,e,r,i){var n=I({},i=i||{});n.font=e,r=Z(r,"..."),n.maxIterations=Z(i.maxIterations,2);var o=n.minChar=Z(i.minChar,0);n.cnCharWidth=fn("国",e);var a=n.ascCharWidth=fn("a",e);n.placeholder=Z(i.placeholder,"");for(var s=t=Math.max(0,t-1),h=0;h<o&&a<=s;h++)s-=a;var l=fn(r,e);return s<l&&(r="",l=0),s=t-l,n.ellipsis=r,n.ellipsisWidth=l,n.contentWidth=s,n.containerWidth=t,n}function Rh(t,e){var r=e.containerWidth,i=e.font,n=e.contentWidth;if(!r)return"";if((s=fn(t,i))<=r)return t;for(var o=0;;o++){if(s<=n||o>=e.maxIterations){t+=e.ellipsis;break}var a=0===o?function(t,e,r,i){for(var n=0,o=0,a=t.length;o<a&&n<e;o++){var s=t.charCodeAt(o);n+=0<=s&&s<=127?r:i}return o}(t,n,e.ascCharWidth,e.cnCharWidth):0<s?Math.floor(t.length*n/s):0,s=fn(t=t.substr(0,a),i)}return""===t&&(t=e.placeholder),t}var Fh=function(){},Bh=function(t){this.tokens=[],t&&(this.tokens=t)},Hh=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function Nh(t,e){var r=new Hh;if(null!=t&&(t+=""),!t)return r;for(var i,n=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==n?null:{width:n,accumWidth:0,breakAll:"breakAll"===a},h=Ih.lastIndex=0;null!=(i=Ih.exec(t));){var l=i.index;h<l&&Eh(r,t.substring(h,l),e,s),Eh(r,i[2],e,s,i[1]),h=Ih.lastIndex}h<t.length&&Eh(r,t.substring(h,t.length),e,s);var u=[],c=0,p=0,f=e.padding,d="truncate"===a,y="truncate"===e.lineOverflow;function g(t,e,r){t.width=e,t.lineHeight=r,c+=r,p=Math.max(p,e)}t:for(var v=0;v<r.lines.length;v++){for(var _=r.lines[v],m=0,x=0,w=0;w<_.tokens.length;w++){var b=(z=_.tokens[w]).styleName&&e.rich[z.styleName]||{},S=z.textPadding=b.padding,k=S?S[1]+S[3]:0,T=z.font=b.font||e.font;z.contentHeight=vn(T);var C=Z(b.height,z.contentHeight);if(z.innerHeight=C,S&&(C+=S[0]+S[2]),z.height=C,z.lineHeight=K(b.lineHeight,e.lineHeight,C),z.align=b&&b.align||e.align,z.verticalAlign=b&&b.verticalAlign||"middle",y&&null!=o&&c+z.lineHeight>o){0<w?(_.tokens=_.tokens.slice(0,w),g(_,x,m),r.lines=r.lines.slice(0,v+1)):r.lines=r.lines.slice(0,v);break t}var P,M,A,L=b.width,D=null==L||"auto"===L;"string"==typeof L&&"%"===L.charAt(L.length-1)?(z.percentWidth=L,u.push(z),z.contentWidth=fn(z.text,T)):(!D||(M=(P=b.backgroundColor)&&P.image)&&zh(M=function(t){if("string"!=typeof t)return t;var e=Ah.get(t);return e&&e.image}(M))&&(z.width=Math.max(z.width,M.width*C/M.height)),null!=(A=d&&null!=n?n-x:null)&&A<z.width?!D||A<k?(z.text="",z.width=z.contentWidth=0):(z.text=function(t,e,r,i,n){if(!e)return"";var o=(t+"").split("\n");n=Oh(e,r,i,n);for(var a=0,s=o.length;a<s;a++)o[a]=Rh(o[a],n);return o.join("\n")}(z.text,A-k,T,e.ellipsis,{minChar:e.truncateMinChar}),z.width=z.contentWidth=fn(z.text,T)):z.contentWidth=fn(z.text,T)),z.width+=k,x+=z.width,b&&(m=Math.max(m,z.lineHeight))}g(_,x,m)}r.outerWidth=r.width=Z(n,p),r.outerHeight=r.height=Z(o,c),r.contentHeight=c,r.contentWidth=p,f&&(r.outerWidth+=f[1]+f[3],r.outerHeight+=f[0]+f[2]);for(v=0;v<u.length;v++){var z,I=(z=u[v]).percentWidth;z.width=parseInt(I,10)/100*r.width}return r}function Eh(t,e,r,i,n){var o,a,s,h,l,u,c=""===e,p=n&&r.rich[n]||{},f=t.lines,d=p.font||r.font,y=!1;i?(h=(s=p.padding)?s[1]+s[3]:0,null!=p.width&&"auto"!==p.width?(l=_n(p.width,i.width)+h,0<f.length&&l+i.accumWidth>i.width&&(o=e.split("\n"),y=!0),i.accumWidth=l):(u=Xh(e,d,i.width,i.breakAll,i.accumWidth),i.accumWidth=u.accumWidth+h,a=u.linesWidths,o=u.lines)):o=e.split("\n");for(var g=0;g<o.length;g++){var v,_,m=o[g],x=new Fh;x.styleName=n,x.text=m,x.isLineHolder=!m&&!c,x.width="number"==typeof p.width?p.width:a?a[g]:fn(m,d),g||y?f.push(new Bh([x])):1===(_=(v=(f[f.length-1]||(f[0]=new Bh)).tokens).length)&&v[0].isLineHolder?v[0]=x:!m&&_&&!c||v.push(x)}}var Wh=D(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function Xh(t,e,r,i,n){for(var o,a,s=[],h=[],l="",u="",c=0,p=0,f=0;f<t.length;f++){var d,y,g=t.charAt(f);"\n"!==g?(d=fn(g,e),y=!i&&(a=void 0,!(!(32<=(a=(o=g).charCodeAt(0))&&a<=591||880<=a&&a<=4351||4608<=a&&a<=5119||7680<=a&&a<=8303)||Wh[o])),(s.length?r<p+d:r<n+p+d)?p?(l||u)&&(p=y?(l||(l=u,u="",p=c=0),s.push(l),h.push(p-c),u+=g,l="",c+=d):(u&&(l+=u,u="",c=0),s.push(l),h.push(p),l=g,d)):y?(s.push(u),h.push(c),u=g,c=d):(s.push(g),h.push(d)):(p+=d,y?(u+=g,c+=d):(u&&(l+=u,u="",c=0),l+=g))):(u&&(l+=u,p+=c),s.push(l),h.push(p),u=l="",p=c=0)}return s.length||l||(l=t,u="",c=0),u&&(l+=u),l&&(s.push(l),h.push(p)),1===s.length&&(p+=n),{accumWidth:p,lines:s,linesWidths:h}}var qh,Yh={fill:"#000"},jh={style:k({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},Wn.style)},Vh=(yt(Uh,qh=Vn),Uh.prototype.childrenRef=function(){return this._children},Uh.prototype.update=function(){qh.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},Uh.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):qh.prototype.updateTransform.call(this)},Uh.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):qh.prototype.getLocalTransform.call(this,t)},Uh.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),qh.prototype.getComputedTransform.call(this)},Uh.prototype._updateSubTexts=function(){var t;this._childCursor=0,tl(t=this.style),O(t.rich,tl),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Uh.prototype.addSelfToZr=function(t){qh.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},Uh.prototype.removeSelfFromZr=function(t){qh.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},Uh.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Se(0,0,0,0),e=this._children,r=[],i=null,n=0;n<e.length;n++){var o=e[n],a=o.getBoundingRect(),s=o.getLocalTransform(r);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},Uh.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Yh},Uh.prototype.setTextContent=function(t){},Uh.prototype._mergeStyle=function(t,e){if(!e)return t;var r=e.rich,i=t.rich||r&&{};return I(t,e),r&&i?(this._mergeRich(i,r),t.rich=i):i&&(t.rich=i),t},Uh.prototype._mergeRich=function(t,e){for(var r=F(e),i=0;i<r.length;i++){var n=r[i];t[n]=t[n]||{},I(t[n],e[n])}},Uh.prototype.getAnimationStyleProps=function(){return jh},Uh.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},Uh.prototype._updatePlainTexts=function(){var t,e,r=this.style,i=r.font||E,n=r.padding,o=function(t,e){null!=t&&(t+="");var r,i,n=e.overflow,o=e.padding,a=e.font,s="truncate"===n,h=vn(a),l=Z(e.lineHeight,h),u=!!e.backgroundColor,c="truncate"===e.lineOverflow,p=e.width,f=(i=null==p||"break"!==n&&"breakAll"!==n?t?t.split("\n"):[]:t?Xh(t,e.font,p,"breakAll"===n,0).lines:[]).length*l,d=Z(e.height,f);if(d<f&&c&&(r=Math.floor(d/l),i=i.slice(0,r)),t&&s&&null!=p)for(var y=Oh(p,a,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),g=0;g<i.length;g++)i[g]=Rh(i[g],y);for(var v=d,_=0,g=0;g<i.length;g++)_=Math.max(fn(i[g],a),_);null==p&&(p=_);var m=_;return o&&(v+=o[0]+o[2],m+=o[1]+o[3],p+=o[1]+o[3]),u&&(m=p),{lines:i,height:d,outerWidth:m,outerHeight:v,lineHeight:l,calculatedLineHeight:h,contentWidth:_,contentHeight:f,width:p}}(nl(r),r),a=ol(r),s=!!r.backgroundColor,h=o.outerHeight,l=o.outerWidth,u=o.contentWidth,c=o.lines,p=o.lineHeight,f=this._defaultStyle,d=r.x||0,y=r.y||0,g=r.align||f.align||"left",v=r.verticalAlign||f.verticalAlign||"top",_=d,m=gn(y,o.contentHeight,v);(a||n)&&(t=yn(d,l,g),e=gn(y,h,v),a&&this._renderBackground(r,r,t,e,l,h)),m+=p/2,n&&(_=il(d,g,n),"top"===v?m+=n[0]:"bottom"===v&&(m-=n[2]));for(var x=0,w=!1,b=(rl("fill"in r?r.fill:(w=!0,f.fill))),S=(el("stroke"in r?r.stroke:s||f.autoStroke&&!w?null:(x=2,f.stroke))),k=0<r.textShadowBlur,T=null!=r.width&&("truncate"===r.overflow||"break"===r.overflow||"breakAll"===r.overflow),C=o.calculatedLineHeight,P=0;P<c.length;P++){var M=this._getOrCreateChild(cs),A=M.createStyle();M.useStyle(A),A.text=c[P],A.x=_,A.y=m,g&&(A.textAlign=g),A.textBaseline="middle",A.opacity=r.opacity,A.strokeFirst=!0,k&&(A.shadowBlur=r.textShadowBlur||0,A.shadowColor=r.textShadowColor||"transparent",A.shadowOffsetX=r.textShadowOffsetX||0,A.shadowOffsetY=r.textShadowOffsetY||0),A.stroke=S,A.fill=b,S&&(A.lineWidth=r.lineWidth||x,A.lineDash=r.lineDash,A.lineDashOffset=r.lineDashOffset||0),A.font=i,$h(A,r),m+=p,T&&M.setBoundingRect(new Se(yn(A.x,r.width,A.textAlign),gn(A.y,C,A.textBaseline),u,C))}},Uh.prototype._updateRichTexts=function(){var t=this.style,e=Nh(nl(t),t),r=e.width,i=e.outerWidth,n=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,h=this._defaultStyle,l=t.align||h.align,u=t.verticalAlign||h.verticalAlign,c=yn(a,i,l),p=gn(s,n,u),f=c,d=p;o&&(f+=o[3],d+=o[0]);var y=f+r;ol(t)&&this._renderBackground(t,t,c,p,i,n);for(var g=!!t.backgroundColor,v=0;v<e.lines.length;v++){for(var _=e.lines[v],m=_.tokens,x=m.length,w=_.lineHeight,b=_.width,S=0,k=f,T=y,C=x-1,P=void 0;S<x&&(!(P=m[S]).align||"left"===P.align);)this._placeToken(P,t,w,d,k,"left",g),b-=P.width,k+=P.width,S++;for(;0<=C&&"right"===(P=m[C]).align;)this._placeToken(P,t,w,d,T,"right",g),b-=P.width,T-=P.width,C--;for(k+=(r-(k-f)-(y-T)-b)/2;S<=C;)P=m[S],this._placeToken(P,t,w,d,k+P.width/2,"center",g),k+=P.width,S++;d+=w}},Uh.prototype._placeToken=function(t,e,r,i,n,o,a){var s=e.rich[t.styleName]||{};s.text=t.text;var h=t.verticalAlign,l=i+r/2;"top"===h?l=i+t.height/2:"bottom"===h&&(l=i+r-t.height/2),!t.isLineHolder&&ol(s)&&this._renderBackground(s,e,"right"===o?n-t.width:"center"===o?n-t.width/2:n,l-t.height/2,t.width,t.height);var u=!!s.backgroundColor,c=t.textPadding;c&&(n=il(n,o,c),l-=t.height/2-c[0]-t.innerHeight/2);var p=this._getOrCreateChild(cs),f=p.createStyle();p.useStyle(f);var d=this._defaultStyle,y=!1,g=0,v=rl("fill"in s?s.fill:"fill"in e?e.fill:(y=!0,d.fill)),_=el("stroke"in s?s.stroke:"stroke"in e?e.stroke:u||a||d.autoStroke&&!y?null:(g=2,d.stroke)),m=0<s.textShadowBlur||0<e.textShadowBlur;f.text=t.text,f.x=n,f.y=l,m&&(f.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,f.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",f.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,f.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),f.textAlign=o,f.textBaseline="middle",f.font=t.font||E,f.opacity=K(s.opacity,e.opacity,1),$h(f,s),_&&(f.lineWidth=K(s.lineWidth,e.lineWidth,g),f.lineDash=Z(s.lineDash,e.lineDash),f.lineDashOffset=e.lineDashOffset||0,f.stroke=_),v&&(f.fill=v);var x=t.contentWidth,w=t.contentHeight;p.setBoundingRect(new Se(yn(f.x,x,f.textAlign),gn(f.y,w,f.textBaseline),x,w))},Uh.prototype._renderBackground=function(t,e,r,i,n,o){var a,s,h,l,u,c=t.backgroundColor,p=t.borderWidth,f=t.borderColor,d=c&&c.image,y=c&&!d,g=t.borderRadius,v=this;(y||t.lineHeight||p&&f)&&((a=this._getOrCreateChild(Oa)).useStyle(a.createStyle()),a.style.fill=null,(h=a.shape).x=r,h.y=i,h.width=n,h.height=o,h.r=g,a.dirtyShape()),y?((u=a.style).fill=c||null,u.fillOpacity=Z(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(Sa)).onload=function(){v.dirtyStyle()},(l=s.style).image=c.image,l.x=r,l.y=i,l.width=n,l.height=o),p&&f&&((u=a.style).lineWidth=p,u.stroke=f,u.strokeOpacity=Z(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2));var _=(a||s).style;_.shadowBlur=t.shadowBlur||0,_.shadowColor=t.shadowColor||"transparent",_.shadowOffsetX=t.shadowOffsetX||0,_.shadowOffsetY=t.shadowOffsetY||0,_.opacity=K(t.opacity,e.opacity,1)},Uh.makeFont=function(t){var e="";return Jh(t)&&(e=[t.fontStyle,t.fontWeight,Qh(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&tt(e)||t.textFont||t.font},Uh);function Uh(t){var e=qh.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=Yh,e.attr(t),e}var Gh={left:!0,right:1,center:1},Zh={top:1,bottom:1,middle:1},Kh=["fontStyle","fontWeight","fontSize","fontFamily"];function Qh(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?h+"px":t+"px":t}function $h(t,e){for(var r=0;r<Kh.length;r++){var i=Kh[r],n=e[i];null!=n&&(t[i]=n)}}function Jh(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function tl(t){var e,r;t&&(t.font=Vh.makeFont(t),"middle"===(e=t.align)&&(e="center"),t.align=null==e||Gh[e]?e:"left","center"===(r=t.verticalAlign)&&(r="middle"),t.verticalAlign=null==r||Zh[r]?r:"top",t.padding&&(t.padding=$(t.padding)))}function el(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function rl(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function il(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}function nl(t){var e=t.text;return null!=e&&(e+=""),e}function ol(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var al,sl=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},hl=(yt(ll,al=Zo),ll.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ll.prototype.getDefaultShape=function(){return new sl},ll.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,h=Math.cos(o),l=Math.sin(o);t.moveTo(h*n+r,l*n+i),t.arc(r,i,n,o,a,!s)},ll);function ll(t){return al.call(this,t)||this}hl.prototype.type="arc";var ul=[],cl=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function pl(t,e,r){var i=t.cpx2,n=t.cpy2;return null!=i||null!=n?[(r?fr:pr)(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?fr:pr)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?_r:vr)(t.x1,t.cpx1,t.x2,e),(r?_r:vr)(t.y1,t.cpy1,t.y2,e)]}var fl,dl=(yt(yl,fl=Zo),yl.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},yl.prototype.getDefaultShape=function(){return new cl},yl.prototype.buildPath=function(t,e){var r=e.x1,i=e.y1,n=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,h=e.cpx2,l=e.cpy2,u=e.percent;0!==u&&(t.moveTo(r,i),null==h||null==l?(u<1&&(xr(r,a,n,u,ul),a=ul[1],n=ul[2],xr(i,s,o,u,ul),s=ul[1],o=ul[2]),t.quadraticCurveTo(a,s,n,o)):(u<1&&(gr(r,a,h,n,u,ul),a=ul[1],h=ul[2],n=ul[3],gr(i,s,l,o,u,ul),s=ul[1],l=ul[2],o=ul[3]),t.bezierCurveTo(a,s,h,l,n,o)))},yl.prototype.pointAt=function(t){return pl(this.shape,t,!1)},yl.prototype.tangentAt=function(t){var e=pl(this.shape,t,!0);return St(e,e)},yl);function yl(t){return fl.call(this,t)||this}dl.prototype.type="bezier-curve";var gl,vl=function(){this.cx=0,this.cy=0,this.width=0,this.height=0},_l=(yt(ml,gl=Zo),ml.prototype.getDefaultShape=function(){return new vl},ml.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.width,o=e.height;t.moveTo(r,i+n),t.bezierCurveTo(r+n,i+n,r+3*n/2,i-n/3,r,i-o),t.bezierCurveTo(r-3*n/2,i-n/3,r-n,i+n,r,i+n),t.closePath()},ml);function ml(t){return gl.call(this,t)||this}_l.prototype.type="droplet";var xl,wl=function(){this.cx=0,this.cy=0,this.width=0,this.height=0},bl=(yt(Sl,xl=Zo),Sl.prototype.getDefaultShape=function(){return new wl},Sl.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.width,o=e.height;t.moveTo(r,i),t.bezierCurveTo(r+n/2,i-2*o/3,r+2*n,i+o/3,r,i+o),t.bezierCurveTo(r-2*n,i+o/3,r-n/2,i-2*o/3,r,i)},Sl);function Sl(t){return xl.call(this,t)||this}bl.prototype.type="heart";var kl,Tl=Math.PI,Cl=Math.sin,Pl=Math.cos,Ml=function(){this.x=0,this.y=0,this.r=0,this.n=0},Al=(yt(Ll,kl=Zo),Ll.prototype.getDefaultShape=function(){return new Ml},Ll.prototype.buildPath=function(t,e){var r=e.n;if(r&&!(r<2)){var i=e.x,n=e.y,o=e.r,a=2*Tl/r,s=-Tl/2;t.moveTo(i+o*Pl(s),n+o*Cl(s));for(var h=0,l=r-1;h<l;h++)s+=a,t.lineTo(i+o*Pl(s),n+o*Cl(s));t.closePath()}},Ll);function Ll(t){return kl.call(this,t)||this}Al.prototype.type="isogon";var Dl,zl=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Il=(yt(Ol,Dl=Zo),Ol.prototype.getDefaultShape=function(){return new zl},Ol.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=2*Math.PI;t.moveTo(r+e.r,i),t.arc(r,i,e.r,0,n,!1),t.moveTo(r+e.r0,i),t.arc(r,i,e.r0,0,n,!0)},Ol);function Ol(t){return Dl.call(this,t)||this}Il.prototype.type="ring";var Rl,Fl=Math.sin,Bl=Math.cos,Hl=Math.PI/180,Nl=function(){this.cx=0,this.cy=0,this.r=[],this.k=0,this.n=1},El=(yt(Wl,Rl=Zo),Wl.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Wl.prototype.getDefaultShape=function(){return new Nl},Wl.prototype.buildPath=function(t,e){var r,i,n,o=e.r,a=e.k,s=e.n,h=e.cx,l=e.cy;t.moveTo(h,l);for(var u=0,c=o.length;u<c;u++){n=o[u];for(var p=0;p<=360*s;p++)r=n*Fl(a/s*p%360*Hl)*Bl(p*Hl)+h,i=n*Fl(a/s*p%360*Hl)*Fl(p*Hl)+l,t.lineTo(r,i)}},Wl);function Wl(t){return Rl.call(this,t)||this}El.prototype.type="rose";var Xl,ql=Math.PI,Yl=Math.cos,jl=Math.sin,Vl=function(){this.cx=0,this.cy=0,this.n=3,this.r=0},Ul=(yt(Gl,Xl=Zo),Gl.prototype.getDefaultShape=function(){return new Vl},Gl.prototype.buildPath=function(t,e){var r=e.n;if(r&&!(r<2)){var i=e.cx,n=e.cy,o=e.r,a=e.r0;null==a&&(a=4<r?o*Yl(2*ql/r)/Yl(ql/r):o/3);var s=ql/r,h=-ql/2,l=i+o*Yl(h),u=n+o*jl(h);h+=s,t.moveTo(l,u);for(var c,p=0,f=2*r-1;p<f;p++)c=p%2==0?a:o,t.lineTo(i+c*Yl(h),n+c*jl(h)),h+=s;t.closePath()}},Gl);function Gl(t){return Xl.call(this,t)||this}Ul.prototype.type="star";var Zl,Kl=Math.cos,Ql=Math.sin,$l=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0,this.d=0,this.location="out"},Jl=(yt(tu,Zl=Zo),tu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},tu.prototype.getDefaultShape=function(){return new $l},tu.prototype.buildPath=function(t,e){var r,i,n=e.r,o=e.r0,a=e.d,s=e.cx,h=e.cy,l="out"===e.location?1:-1;if(!(e.location&&n<=o)){var u,c=0,p=1,f=(n+l*o)*Kl(0)-l*a*Kl(0)+s,d=(n+l*o)*Ql(0)-a*Ql(0)+h;for(t.moveTo(f,d);o*++c%(n+l*o)!=0;);for(;u=Math.PI/180*p,r=(n+l*o)*Kl(u)-l*a*Kl((n/o+l)*u)+s,i=(n+l*o)*Ql(u)-a*Ql((n/o+l)*u)+h,t.lineTo(r,i),++p<=o*c/(n+l*o)*360;);}},tu);function tu(t){return Zl.call(this,t)||this}Jl.prototype.type="trochoid";function eu(t,e){this.image=t,this.repeat=e,this.x=0,this.y=0,this.rotation=0,this.scaleX=1,this.scaleY=1}var ru=[0,0],iu=[0,0],nu=new fe,ou=new fe,au=(su.prototype.fromBoundingRect=function(t,e){var r=this._corners,i=this._axes,n=t.x,o=t.y,a=n+t.width,s=o+t.height;if(r[0].set(n,o),r[1].set(a,o),r[2].set(a,s),r[3].set(n,s),e)for(var h=0;h<4;h++)r[h].transform(e);for(fe.sub(i[0],r[1],r[0]),fe.sub(i[1],r[3],r[0]),i[0].normalize(),i[1].normalize(),h=0;h<2;h++)this._origin[h]=i[h].dot(r[0])},su.prototype.intersect=function(t,e){var r=!0,i=!e;return nu.set(1/0,1/0),ou.set(0,0),!this._intersectCheckOneSide(this,t,nu,ou,i,1)&&(r=!1,i)||!this._intersectCheckOneSide(t,this,nu,ou,i,-1)&&(r=!1,i)||i||fe.copy(e,r?nu:ou),r},su.prototype._intersectCheckOneSide=function(t,e,r,i,n,o){for(var a=!0,s=0;s<2;s++){var h=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,ru),this._getProjMinMaxOnAxis(s,e._corners,iu),ru[1]<iu[0]||iu[1]<ru[0]){if(a=!1,n)return a;var l=Math.abs(iu[0]-ru[1]),u=Math.abs(ru[0]-iu[1]);Math.min(l,u)>i.len()&&(l<u?fe.scale(i,h,-l*o):fe.scale(i,h,u*o))}else r&&(l=Math.abs(iu[0]-ru[1]),u=Math.abs(ru[0]-iu[1]),Math.min(l,u)<r.len()&&(l<u?fe.scale(r,h,l*o):fe.scale(r,h,-u*o)))}return a},su.prototype._getProjMinMaxOnAxis=function(t,e,r){for(var i=this._axes[t],n=this._origin,o=e[0].dot(i)+n[t],a=o,s=o,h=1;h<e.length;h++)var l=e[h].dot(i)+n[t],a=Math.min(l,a),s=Math.max(l,s);r[0]=a,r[1]=s},su);function su(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var r=0;r<4;r++)this._corners[r]=new fe;for(r=0;r<2;r++)this._axes[r]=new fe;t&&this.fromBoundingRect(t,e)}var hu=(lu.prototype.update=function(t){var e=this.dom.style;e.width=t.width+"px",e.height=t.height+"px",e.left=t.x+"px",e.top=t.y+"px"},lu.prototype.hide=function(){this.dom.style.opacity="0"},lu.prototype.show=function(t){var e=this;clearTimeout(this._hideTimeout),this.dom.style.opacity="1",this._hideTimeout=setTimeout(function(){e.hide()},t||1e3)},lu);function lu(t){var e=this.dom=document.createElement("div");for(var r in e.className="ec-debug-dirty-rect",t=I({},t),I(t,{backgroundColor:"rgba(0, 0, 255, 0.2)",border:"1px solid #00f"}),e.style.cssText="\nposition: absolute;\nopacity: 0;\ntransition: opacity 0.5s linear;\npointer-events: none;\n",t)t.hasOwnProperty(r)&&(e.style[r]=t[r])}function uu(t){return isFinite(t)}function cu(t,e,r){for(var i,n,o,a,s,h,l,u,c,p,f,d,y,g,v,_,m="radial"===e.type?(u=t,c=e,f=(p=r).width,d=p.height,y=Math.min(f,d),g=null==c.x?.5:c.x,v=null==c.y?.5:c.y,_=null==c.r?.5:c.r,c.global||(g=g*f+p.x,v=v*d+p.y,_*=y),g=uu(g)?g:.5,v=uu(v)?v:.5,_=0<=_&&uu(_)?_:.5,u.createRadialGradient(g,v,0,g,v,_)):(i=t,o=r,a=null==(n=e).x?0:n.x,s=null==n.x2?1:n.x2,h=null==n.y?0:n.y,l=null==n.y2?0:n.y2,n.global||(a=a*o.width+o.x,s=s*o.width+o.x,h=h*o.height+o.y,l=l*o.height+o.y),a=uu(a)?a:0,s=uu(s)?s:1,h=uu(h)?h:0,l=uu(l)?l:0,i.createLinearGradient(a,h,s,l)),x=e.colorStops,w=0;w<x.length;w++)m.addColorStop(x[w].offset,x[w].color);return m}function pu(t){return parseInt(t,10)}function fu(t,e,r){var i=["width","height"][e],n=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=r[i]&&"auto"!==r[i])return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(t);return(t[n]||pu(s[i])||pu(t.style[i]))-(pu(s[o])||0)-(pu(s[a])||0)|0}function du(t){var e,r,i,n=t.style,o=n.lineDash&&0<n.lineWidth&&(e=n.lineDash,r=n.lineWidth,e&&"solid"!==e&&0<r?"dashed"===e?[4*r,2*r]:"dotted"===e?[r]:N(e)?[e]:G(e)?e:null:null),a=n.lineDashOffset;return!o||(i=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==i&&(o=L(o,function(t){return t/i}),a/=i),[o,a]}var yu=new Po(!0);function gu(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function vu(t){return"string"==typeof t&&"none"!==t}function _u(t){var e=t.fill;return null!=e&&"none"!==e}function mu(t,e){var r;null!=e.fillOpacity&&1!==e.fillOpacity?(r=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=r):t.fill()}function xu(t,e){var r;null!=e.strokeOpacity&&1!==e.strokeOpacity?(r=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=r):t.stroke()}function wu(t,e,r){var i=Lh(e.image,e.__image,r);if(zh(i)){var n,o=t.createPattern(i,e.repeat||"repeat");return"function"==typeof DOMMatrix&&o&&o.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*pt),n.scaleSelf(e.scaleX||1,e.scaleY||1),o.setTransform(n)),o}}var bu=["shadowBlur","shadowOffsetX","shadowOffsetY"],Su=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function ku(t,e,r,i,n){var o,a=!1;if(!i&&e===(r=r||{}))return!1;!i&&e.opacity===r.opacity||(Du(t,n),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?En.opacity:o),!i&&e.blend===r.blend||(a||(Du(t,n),a=!0),t.globalCompositeOperation=e.blend||En.blend);for(var s=0;s<bu.length;s++){var h=bu[s];!i&&e[h]===r[h]||(a||(Du(t,n),a=!0),t[h]=t.dpr*(e[h]||0))}return!i&&e.shadowColor===r.shadowColor||(a||(Du(t,n),a=!0),t.shadowColor=e.shadowColor||En.shadowColor),a}function Tu(t,e,r,i,n){var o=zu(e,n.inHover),a=i?null:r&&zu(r,n.inHover)||{};if(o!==a){var s,h=ku(t,o,a,i,n);!i&&o.fill===a.fill||(h||(Du(t,n),h=!0),vu(o.fill)&&(t.fillStyle=o.fill)),!i&&o.stroke===a.stroke||(h||(Du(t,n),h=!0),vu(o.stroke)&&(t.strokeStyle=o.stroke)),!i&&o.opacity===a.opacity||(h||(Du(t,n),h=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(s=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==s&&(h||(Du(t,n),h=!0),t.lineWidth=s));for(var l=0;l<Su.length;l++){var u=Su[l],c=u[0];!i&&o[c]===a[c]||(h||(Du(t,n),h=!0),t[c]=o[c]||u[1])}return h}}function Cu(t,e){var r=e.transform,i=t.dpr||1;r?t.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):t.setTransform(i,0,0,i,0,0)}var Pu=1,Mu=2,Au=3,Lu=4;function Du(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function zu(t,e){return e&&t.__hoverStyle||t.style}function Iu(t,e,r,i){var n=e.transform;if(!e.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1))return e.__dirty&=~Ve,void(e.__isRendered=!1);var o,a,s,h,l,u,c,p,f,d,y,g,v,_,m,x,w,b,S,k,T,C,P,M,A,L,D,z,I,O,R,F=e.__clipPaths,B=r.prevElClipPaths,H=!1,N=!1;B&&!function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return 1}}(F,B)||(B&&B.length&&(Du(t,r),t.restore(),N=H=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),F&&F.length&&(Du(t,r),t.save(),function(t,e,r){for(var i=!1,n=0;n<t.length;n++){var o=t[n],i=i||o.isZeroArea();Cu(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}r.allClipped=i}(F,t,r),H=!0),r.prevElClipPaths=F),r.allClipped?e.__isRendered=!1:(e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush(),(o=r.prevEl)||(N=H=!0),a=e instanceof Zo&&e.autoBatch&&(s=e.style,h=_u(s),l=gu(s),!(s.lineDash||!(+h^+l)||h&&"string"!=typeof s.fill||l&&"string"!=typeof s.stroke||s.strokePercent<1||s.strokeOpacity<1||s.fillOpacity<1)),H||(u=n,c=o.transform,u&&c?u[0]!==c[0]||u[1]!==c[1]||u[2]!==c[2]||u[3]!==c[3]||u[4]!==c[4]||u[5]!==c[5]:u||c)?(Du(t,r),Cu(t,e)):a||Du(t,r),p=zu(e,r.inHover),e instanceof Zo?(r.lastDrawType!==Pu&&(N=!0,r.lastDrawType=Pu),Tu(t,e,o,N,r),a&&(r.batchFill||r.batchStroke)||t.beginPath(),function(t,e,r,i){var n,o=gu(r),a=_u(r),s=r.strokePercent,h=s<1,l=!e.path;e.silent&&!h||!l||e.createPathProxy();var u,c,p,f,d,y,g,v,_,m,x,w=e.path||yu,b=e.__dirty;i||(u=r.fill,c=r.stroke,p=a&&!!u.colorStops,f=o&&!!c.colorStops,d=a&&!!u.image,y=o&&!!c.image,x=m=_=v=g=void 0,(p||f)&&(x=e.getBoundingRect()),p&&(g=b?cu(t,u,x):e.__canvasFillGradient,e.__canvasFillGradient=g),f&&(v=b?cu(t,c,x):e.__canvasStrokeGradient,e.__canvasStrokeGradient=v),d&&(_=b||!e.__canvasFillPattern?wu(t,u,e):e.__canvasFillPattern,e.__canvasFillPattern=_),y&&(m=b||!e.__canvasStrokePattern?wu(t,c,e):e.__canvasStrokePattern,e.__canvasStrokePattern=_),p?t.fillStyle=g:d&&(_?t.fillStyle=_:a=!1),f?t.strokeStyle=v:y&&(m?t.strokeStyle=m:o=!1));var S,k,T=e.getGlobalScale();w.setScale(T[0],T[1],e.segmentIgnoreThreshold),t.setLineDash&&r.lineDash&&(S=(n=du(e))[0],k=n[1]);var C=!0;(l||b&Ue)&&(w.setDPR(t.dpr),h?w.setContext(null):(w.setContext(t),C=!1),w.reset(),e.buildPath(w,e.shape,i),w.toStatic(),e.pathUpdated()),C&&w.rebuildPath(t,h?s:1),S&&(t.setLineDash(S),t.lineDashOffset=k),i||(r.strokeFirst?(o&&xu(t,r),a&&mu(t,r)):(a&&mu(t,r),o&&xu(t,r))),S&&t.setLineDash([])}(t,e,p,a),a&&(r.batchFill=p.fill||"",r.batchStroke=p.stroke||"")):e instanceof cs?(r.lastDrawType!==Au&&(N=!0,r.lastDrawType=Au),Tu(t,e,o,N,r),A=t,L=e,null!=(R=(D=p).text)&&(R+=""),R&&(A.font=D.font||E,A.textAlign=D.textAlign,A.textBaseline=D.textBaseline,O=I=void 0,A.setLineDash&&D.lineDash&&(I=(z=du(L))[0],O=z[1]),I&&(A.setLineDash(I),A.lineDashOffset=O),D.strokeFirst?(gu(D)&&A.strokeText(R,D.x,D.y),_u(D)&&A.fillText(R,D.x,D.y)):(_u(D)&&A.fillText(R,D.x,D.y),gu(D)&&A.strokeText(R,D.x,D.y)),I&&A.setLineDash([]))):e instanceof Sa?(r.lastDrawType!==Mu&&(N=!0,r.lastDrawType=Mu),C=o,P=N,ku(t,zu(e,(M=r).inHover),C&&zu(C,M.inHover),P,M),f=t,y=p,(T=(d=e).__image=Lh(y.image,d.__image,d,d.onload))&&zh(T)&&(g=y.x||0,v=y.y||0,_=d.getWidth(),m=d.getHeight(),x=T.width/T.height,null==_&&null!=m?_=m*x:null==m&&null!=_?m=_/x:null==_&&null==m&&(_=T.width,m=T.height),y.sWidth&&y.sHeight?(w=y.sx||0,b=y.sy||0,f.drawImage(T,w,b,y.sWidth,y.sHeight,g,v,_,m)):y.sx&&y.sy?(S=_-(w=y.sx),k=m-(b=y.sy),f.drawImage(T,w,b,S,k,g,v,_,m)):f.drawImage(T,g,v,_,m))):e.getTemporalDisplayables&&(r.lastDrawType!==Lu&&(N=!0,r.lastDrawType=Lu),function(t,e,r){var i=e.getDisplayables(),n=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(u=i[o]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Iu(t,u,s,o===a-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}for(var h=0,l=n.length;h<l;h++){var u;(u=n[h]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Iu(t,u,s,h===l-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,r)),a&&i&&Du(t,r),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(r.prevEl=e).__dirty=0,e.__isRendered=!0)}function Ou(t,e,r){var i=f.createCanvas(),n=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=n+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=n*r,i.height=o*r,i}var Ru,Fu=(yt(Bu,Ru=Ft),Bu.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},Bu.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},Bu.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},Bu.prototype.setUnpainted=function(){this.__firstTimePaint=!0},Bu.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Ou("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},Bu.prototype.createRepaintRects=function(t,e,r,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var c=[],p=this.maxRepaintRectCount,f=!1,d=new Se(0,0,0,0);function n(t){if(t.isFinite()&&!t.isZero())if(0===c.length)(e=new Se(0,0,0,0)).copy(t),c.push(e);else{for(var e,r=!1,i=1/0,n=0,o=0;o<c.length;++o){var a,s,h,l=c[o];if(l.intersect(t)){var u=new Se(0,0,0,0);u.copy(l),u.union(t),c[o]=u,r=!0;break}f&&(d.copy(t),d.union(l),a=t.width*t.height,s=l.width*l.height,(h=d.width*d.height-a-s)<i&&(i=h,n=o))}f&&(c[n].union(t),r=!0),r||((e=new Se(0,0,0,0)).copy(t),c.push(e)),f=f||c.length>=p}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(h=t[a])&&(u=h.shouldBePainted(r,i,!0,!0),(l=h.__isRendered&&(h.__dirty&Ve||!u)?h.getPrevPaintRect():null)&&n(l),(o=u&&(h.__dirty&Ve||!h.__isRendered)?h.getPaintRect():null)&&n(o));for(var s,a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var h,l,u=(h=e[a]).shouldBePainted(r,i,!0,!0);!h||u&&h.__zr||!h.__isRendered||(l=h.getPrevPaintRect())&&n(l)}do{for(s=!1,a=0;a<c.length;)if(c[a].isZero())c.splice(a,1);else{for(var y=a+1;y<c.length;)c[a].intersect(c[y])?(s=!0,c[a].union(c[y]),c.splice(y,1)):y++;a++}}while(s);return this._paintRects=c},Bu.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},Bu.prototype.resize=function(t,e){var r=this.dpr,i=this.dom,n=i.style,o=this.domBack;n&&(n.width=t+"px",n.height=e+"px"),i.width=t*r,i.height=e*r,o&&(o.width=t*r,o.height=e*r,1!==r&&this.ctxBack.scale(r,r))},Bu.prototype.clear=function(t,o,e){var r=this.dom,a=this.ctx,i=r.width,n=r.height;o=o||this.clearColor;var s=this.motionBlur&&!t,h=this.lastFrameAlpha,l=this.dpr,u=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(r,0,0,i/l,n/l));var c=this.domBack;function p(t,e,r,i){var n;a.clearRect(t,e,r,i),o&&"transparent"!==o&&(n=void 0,j(o)?(n=(o.global||o.__width===r&&o.__height===i)&&o.__canvasGradient||cu(a,o,{x:0,y:0,width:r,height:i}),o.__canvasGradient=n,o.__width=r,o.__height=i):V(o)&&(o.scaleX=o.scaleX||l,o.scaleY=o.scaleY||l,n=wu(a,o,{dirty:function(){u.setUnpainted(),u.__painter.refresh()}})),a.save(),a.fillStyle=n||o,a.fillRect(t,e,r,i),a.restore()),s&&(a.save(),a.globalAlpha=h,a.drawImage(c,t,e,r,i),a.restore())}!e||s?p(0,0,i,n):e.length&&O(e,function(t){p(t.x*l,t.y*l,t.width*l,t.height*l)})},Bu);function Bu(t,e,r){var i,n=Ru.call(this)||this;n.motionBlur=!1,n.lastFrameAlpha=.7,n.dpr=1,n.virtual=!1,n.config={},n.incremental=!1,n.zlevel=0,n.maxRepaintRectCount=5,n.__dirty=!0,n.__firstTimePaint=!0,n.__used=!1,n.__drawIndex=0,n.__startIndex=0,n.__endIndex=0,n.__prevStartIndex=null,n.__prevEndIndex=null,r=r||Qi,"string"==typeof t?i=Ou(t,e,r):W(t)&&(t=(i=t).id),n.id=t;var o=(n.dom=i).style;return o&&(lt(i),i.onselectstart=function(){return!1},o.padding="0",o.margin="0",o.borderWidth="0"),n.painter=e,n.dpr=r,n}var Hu=314159;var Nu=(Eu.prototype.getType=function(){return"canvas"},Eu.prototype.isSingleCanvas=function(){return this._singleCanvas},Eu.prototype.getViewportRoot=function(){return this._domRoot},Eu.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},Eu.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),r=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,r,t,this._redrawId);for(var n=0;n<i.length;n++){var o,a=i[n],s=this._layers[a];!s.__builtin__&&s.refresh&&(o=0===n?this._backgroundColor:null,s.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},Eu.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},Eu.prototype._paintHoverList=function(t){var e=t.length,r=this._hoverlayer;if(r&&r.clear(),e){for(var i,n={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(r=r||(this._hoverlayer=this.getLayer(1e5)),i||(i=r.ctx).save(),Iu(i,a,n,o===e-1))}i&&i.restore()}},Eu.prototype.getHoverLayer=function(){return this.getLayer(1e5)},Eu.prototype.paintOne=function(t,e){Iu(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)},Eu.prototype._paintList=function(t,e,r,i){var n,o,a,s;this._redrawId===i&&(r=r||!1,this._updateLayerStatus(t),o=(n=this._doPaintList(t,e,r)).finished,a=n.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(s=this,Je(function(){s._paintList(t,e,r,i)})))},Eu.prototype._compositeManually=function(){var e=this.getLayer(Hu).ctx,r=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,r,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,r,i)})},Eu.prototype._doPaintList=function(d,y,g){for(var v=this,_=[],m=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],r=this._layers[e];r.__builtin__&&r!==this._hoverlayer&&(r.__dirty||g)&&_.push(r)}for(var x=!0,w=!1,b=this,i=0;i<_.length;i++)!function(t){var e,i,n=_[t],o=n.ctx,r=m&&n.createRepaintRects(d,y,b._width,b._height),a=g?n.__startIndex:n.__drawIndex,s=!g&&n.incremental&&Date.now,h=s&&Date.now(),l=n.zlevel===b._zlevelList[0]?b._backgroundColor:null;function u(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:v._width,viewHeight:v._height};for(i=a;i<n.__endIndex;i++){var r=d[i];if(r.__inHover&&(w=!0),v._doPaintEl(r,n,m,t,e,i===n.__endIndex-1),s&&15<Date.now()-h)break}e.prevElClipPaths&&o.restore()}if(n.__startIndex===n.__endIndex?n.clear(!1,l,r):a===n.__startIndex&&((e=d[a]).incremental&&e.notClear&&!g||n.clear(!1,l,r)),-1===a&&(console.error("For some unknown reason. drawIndex is -1"),a=n.__startIndex),r)if(0===r.length)i=n.__endIndex;else for(var c=b.dpr,p=0;p<r.length;++p){var f=r[p];o.save(),o.beginPath(),o.rect(f.x*c,f.y*c,f.width*c,f.height*c),o.clip(),u(f),o.restore()}else o.save(),u(),o.restore();n.__drawIndex=i,n.__drawIndex<n.__endIndex&&(x=!1)}(i);return c.wxa&&O(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:x,needsRefreshHover:w}},Eu.prototype._doPaintEl=function(t,e,r,i,n,o){var a,s=e.ctx;r?(a=t.getPaintRect(),(!i||a&&a.intersect(i))&&(Iu(s,t,n,o),t.setPrevPaintRect(a))):Iu(s,t,n,o)},Eu.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Hu);var r=this._layers[t];return r||((r=new Fu("zr_"+t,this,this.dpr)).zlevel=t,r.__builtin__=!0,this._layerConfig[t]?S(r,this._layerConfig[t],!0):this._layerConfig[t-.01]&&S(r,this._layerConfig[t-.01],!0),e&&(r.virtual=e),this.insertLayer(t,r),r.initContext()),r},Eu.prototype.insertLayer=function(t,e){var r,i=this._layers,n=this._zlevelList,o=n.length,a=this._domRoot,s=null,h=-1;if(!i[t]&&function(t){if(t){if(t.__builtin__)return 1;if("function"==typeof t.resize&&"function"==typeof t.refresh)return 1}}(e)){if(0<o&&t>n[0]){for(h=0;h<o-1&&!(n[h]<t&&n[h+1]>t);h++);s=i[n[h]]}n.splice(h+1,0,t),(i[t]=e).virtual||(s?(r=s.dom).nextSibling?a.insertBefore(e.dom,r.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.__painter=this}},Eu.prototype.eachLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i];t.call(e,this._layers[n],n)}},Eu.prototype.eachBuiltinLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__&&t.call(e,o,n)}},Eu.prototype.eachOtherLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__||t.call(e,o,n)}},Eu.prototype.getLayers=function(){return this._layers},Eu.prototype._updateLayerStatus=function(t){function e(t){n&&(n.__endIndex!==t&&(n.__dirty=!0),n.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var r=1;r<t.length;r++)if((s=t[r]).zlevel!==t[r-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,n=null,o=0,a=0;a<t.length;a++){var s,h=(s=t[a]).zlevel,l=void 0;i!==h&&(i=h,o=0),s.incremental?((l=this.getLayer(h+.001,this._needsManuallyCompositing)).incremental=!0,o=1):l=this.getLayer(h+(0<o?.01:0),this._needsManuallyCompositing),l.__builtin__||b("ZLevel "+h+" has been used by unkown layer "+l.id),l!==n&&(l.__used=!0,l.__startIndex!==a&&(l.__dirty=!0),l.__startIndex=a,l.incremental?l.__drawIndex=-1:l.__drawIndex=a,e(a),n=l),s.__dirty&Ve&&!s.__inHover&&(l.__dirty=!0,l.incremental&&l.__drawIndex<0&&(l.__drawIndex=a))}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},Eu.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},Eu.prototype._clearLayer=function(t){t.clear()},Eu.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,function(t){t.setUnpainted()})},Eu.prototype.configLayer=function(t,e){if(e){var r=this._layerConfig;r[t]?S(r[t],e,!0):r[t]=e;for(var i=0;i<this._zlevelList.length;i++){var n=this._zlevelList[i];n!==t&&n!==t+.01||S(this._layers[n],r[t],!0)}}},Eu.prototype.delLayer=function(t){var e=this._layers,r=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],r.splice(P(r,t),1))},Eu.prototype.resize=function(t,e){if(this._domRoot.style){var r=this._domRoot;r.style.display="none";var i=this._opts,n=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=fu(n,0,i),e=fu(n,1,i),r.style.display="",this._width!==t||e!==this._height){for(var o in r.style.width=t+"px",r.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(Hu).resize(t,e)}return this},Eu.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},Eu.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},Eu.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Hu].dom;var e=new Fu("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var r=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(r.save(),t.renderToCanvas(r),r.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,h=a.length;s<h;s++){var l=a[s];Iu(r,l,o,s===h-1)}return e.dom},Eu.prototype.getWidth=function(){return this._width},Eu.prototype.getHeight=function(){return this._height},Eu);function Eu(t,e,r,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=r=I({},r||{}),this.dpr=r.devicePixelRatio||Qi,this._singleCanvas=n,(this.root=t).style&&(lt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a,s,h,l,u,c,p,f,d=this._layers;n?(s=(a=t).width,h=a.height,null!=r.width&&(s=r.width),null!=r.height&&(h=r.height),this.dpr=r.devicePixelRatio||1,a.width=s*this.dpr,a.height=h*this.dpr,this._width=s,this._height=h,(l=new Fu(a,this,this.dpr)).__builtin__=!0,l.initContext(),(d[Hu]=l).zlevel=Hu,o.push(Hu),this._domRoot=t):(this._width=fu(t,0,r),this._height=fu(t,1,r),u=this._domRoot=(c=this._width,p=this._height,(f=document.createElement("div")).style.cssText=["position:relative","width:"+c+"px","height:"+p+"px","padding:0","margin:0","border-width:0"].join(";")+";",f),t.appendChild(u))}var Wu=Math.sin,Xu=Math.cos,qu=Math.PI,Yu=2*Math.PI,ju=180/qu,Vu=(Uu.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},Uu.prototype.moveTo=function(t,e){this._add("M",t,e)},Uu.prototype.lineTo=function(t,e){this._add("L",t,e)},Uu.prototype.bezierCurveTo=function(t,e,r,i,n,o){this._add("C",t,e,r,i,n,o)},Uu.prototype.quadraticCurveTo=function(t,e,r,i){this._add("Q",t,e,r,i)},Uu.prototype.arc=function(t,e,r,i,n,o){this.ellipse(t,e,r,r,0,i,n,o)},Uu.prototype.ellipse=function(t,e,r,i,n,o,a,s){var h=a-o,l=!s,u=Math.abs(h),c=$r(u-Yu)||(l?Yu<=h:Yu<=-h),p=!1,p=!!c||!$r(u)&&qu<=(0<h?h%Yu:h%Yu+Yu)==!!l,f=t+r*Xu(o),d=e+i*Wu(o);this._start&&this._add("M",f,d);var y,g,v,_,m=Math.round(n*ju);c?(y=1/this._p,g=(l?1:-1)*(Yu-y),this._add("A",r,i,m,1,+l,t+r*Xu(o+g),e+i*Wu(o+g)),.01<y&&this._add("A",r,i,m,0,+l,f,d)):(v=t+r*Xu(a),_=e+i*Wu(a),this._add("A",r,i,m,+p,+l,v,_))},Uu.prototype.rect=function(t,e,r,i){this._add("M",t,e),this._add("l",r,0),this._add("l",0,i),this._add("l",-r,0),this._add("Z")},Uu.prototype.closePath=function(){0<this._d.length&&this._add("Z")},Uu.prototype._add=function(t,e,r,i,n,o,a,s,h){for(var l=[],u=this._p,c=1;c<arguments.length;c++){var p=arguments[c];if(isNaN(p))return void(this._invalid=!0);l.push(Math.round(p*u)/u)}this._d.push(t+l.join(" ")),this._start="Z"===t},Uu.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},Uu.prototype.getStr=function(){return this._str},Uu);function Uu(){}var Gu="none",Zu=Math.round;var Ku=["lineCap","miterLimit","lineJoin"],Qu=L(Ku,function(t){return"stroke-"+t.toLowerCase()});function $u(t,e,r,i){var n,o,a,s,h=null==e.opacity?1:e.opacity;if(r instanceof Sa)t("opacity",h);else if(null!=(a=e.fill)&&a!==Gu?(t("fill",(n=Kr(e.fill)).color),o=null!=e.fillOpacity?e.fillOpacity*n.opacity*h:n.opacity*h,(i||o<1)&&t("fill-opacity",o)):t("fill",Gu),null!=(s=e.stroke)&&s!==Gu){var l=Kr(e.stroke);t("stroke",l.color);var u,c,p,f=e.strokeNoScale?r.getLineScale():1,d=f?(e.lineWidth||0)/f:0,y=null!=e.strokeOpacity?e.strokeOpacity*l.opacity*h:l.opacity*h,g=e.strokeFirst;!i&&1==d||t("stroke-width",d),(i||g)&&t("paint-order",g?"stroke":"fill"),(i||y<1)&&t("stroke-opacity",y),e.lineDash?(c=(u=du(r))[0],p=u[1],c&&(p=Zu(p||0),t("stroke-dasharray",c.join(",")),(p||i)&&t("stroke-dashoffset",p))):i&&t("stroke-dasharray",Gu);for(var v=0;v<Ku.length;v++){var _,m=Ku[v];!i&&e[m]===Vo[m]||(_=e[m]||Vo[m])&&t(Qu[v],_)}}else i&&t("stroke",Gu)}var Ju="http://www.w3.org/2000/svg",tc="http://www.w3.org/1999/xlink",ec="http://www.w3.org/2000/xmlns/",rc="http://www.w3.org/XML/1998/namespace";function ic(t){return document.createElementNS(Ju,t)}function nc(t,e,r,i,n){return{tag:t,attrs:r||{},children:i,text:n,key:e}}function oc(t,e){var s=(e=e||{}).newline?"\n":"";return function t(e){var r,i=e.children,n=e.tag,o=e.attrs,a=e.text;return function(t,e){var r=[];if(e)for(var i in e){var n=e[i],o=i;!1!==n&&(!0!==n&&null!=n&&(o+='="'+n+'"'),r.push(o))}return"<"+t+" "+r.join(" ")+">"}(n,o)+("style"!==n?null==(r=a)?"":(r+"").replace(Yt,function(t,e){return jt[e]}):a||"")+(i?""+s+L(i,t).join(s)+s:"")+"</"+n+">"}(t)}function ac(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssClassIdx:0,cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function sc(t,e,r,i){return nc("svg","root",{width:t,height:e,xmlns:Ju,"xmlns:xlink":tc,version:"1.1",baseProfile:"full",viewBox:!!i&&"0 0 "+t+" "+e},r)}var hc={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},lc="transform-origin";var uc={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function cc(t,e){var r=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[r]=t,r}function pc(t){return H(t)?hc[t]?"cubic-bezier("+hc[t]+")":br(t)?t:"":""}function fc(A,L,D,z){var t=A.animators,e=t.length,r=[];if(A instanceof Sh){if(i=function(t,e,c){var p,f,r=t.shape.paths,d={};if(O(r,function(t){var e=ac(c.zrId);e.animation=!0,fc(t,{},e,!0);var r=e.cssAnims,i=e.cssNodes,n=F(r),o=n.length;if(o){var a=r[f=n[o-1]];for(var s in a){var h=a[s];d[s]=d[s]||{d:""},d[s].d+=h.d||""}for(var l in i){var u=i[l].animation;0<=u.indexOf(f)&&(p=u)}}}),p){e.d=!1;var i=cc(d,c);return p.replace(f,i)}}(A,L,D))r.push(i);else if(!e)return}else if(!e)return;for(var i,n,o={},a=0;a<e;a++){var s=t[a],h=[s.getMaxTime()/1e3+"s"],l=pc(s.getClip().easing),u=s.getDelay();l?h.push(l):h.push("linear"),u&&h.push(u/1e3+"s"),s.getLoop()&&h.push("infinite");var c=h.join(" ");o[c]=o[c]||[c,[]],o[c][1].push(s)}function p(t){var e=t[1],r=e.length,i={},n={},o={},d="animation-timing-function";function a(t,e,r){for(var i=t.getTracks(),n=t.getMaxTime(),o=0;o<i.length;o++){var a=i[o];if(a.needsAnimate()){var s=a.keyframes,h=a.propName;if(r&&(h=r(h)),h)for(var l=0;l<s.length;l++){var u=s[l],c=Math.round(u.time/n*100)+"%",p=pc(u.easing),f=u.rawValue;(H(f)||N(f))&&(e[c]=e[c]||{},e[c][h]=u.rawValue,p&&(e[c][d]=p))}}}}for(var s,h,l,u,c,p=0;p<r;p++){(S=(b=e[p]).targetName)?"shape"===S&&a(b,n):z||a(b,i)}for(var f in i){var y={};cn(y,A),I(y,i[f]);var g=li(y),v=i[f][d];o[f]=g?{transform:g}:{},s=o[f],u=l=void 0,l=(h=y).originX,u=h.originY,(l||u)&&(s[lc]=l+"px "+u+"px"),v&&(o[f][d]=v)}var _=!0;for(var f in n){o[f]=o[f]||{};var m=!c,v=n[f][d];m&&(c=new Po);var x=c.len();c.reset(),o[f].d=function(t,e,r){var i=I({},t.shape);I(i,e),t.buildPath(r,i);var n=new Vu;return n.reset(hi(t)),r.rebuildPath(n,1),n.generateStr(),n.getStr()}(A,n[f],c);var w=c.len();if(!m&&x!==w){_=!1;break}v&&(o[f][d]=v)}if(!_)for(var f in o)delete o[f].d;if(!z)for(var b,S,p=0;p<r;p++){"style"===(S=(b=e[p]).targetName)&&a(b,o,function(t){return uc[t]})}for(var k,T=F(o),C=!0,p=1;p<T.length;p++){var P=T[p-1],M=T[p];if(o[P][lc]!==o[M][lc]){C=!1;break}k=o[P][lc]}if(C&&k){for(var f in o)o[f][lc]&&delete o[f][lc];L[lc]=k}if(R(T,function(t){return 0<F(o[t]).length}).length)return cc(o,D)+" "+t[0]+" both"}for(var f in o){(i=p(o[f]))&&r.push(i)}r.length&&(n=D.zrId+"-cls-"+D.cssClassIdx++,D.cssNodes["."+n]={animation:r.join(",")},L.class=n)}var dc=Math.round;function yc(t){return t&&H(t.src)}function gc(t){return t&&B(t.toDataURL)}function vc(i,n,o,a){$u(function(t,e){var r="fill"===t||"stroke"===t;r&&ai(e)?Cc(n,i,t,a):r&&ii(e)?Pc(o,i,t,a):i[t]=e},n,o,!1),function(t,e,r){var i=t.style;if(function(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}(i)){var n=function(t){var e=t.style,r=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),r[0],r[1]].join(",")}(t),o=r.shadowCache,a=o[n];if(!a){var s=t.getGlobalScale(),h=s[0],l=s[1];if(!h||!l)return;var u=i.shadowOffsetX||0,c=i.shadowOffsetY||0,p=i.shadowBlur,f=Kr(i.shadowColor),d=f.opacity,y=f.color,g=p/2/h+" "+p/2/l;a=r.zrId+"-s"+r.shadowIdx++,r.defs[a]=nc("filter",a,{id:a,x:"-100%",y:"-100%",width:"300%",height:"300%"},[nc("feDropShadow","",{dx:u/h,dy:c/l,stdDeviation:g,"flood-color":y,"flood-opacity":d})]),o[n]=a}e.filter=si(a)}}(o,i,a)}function _c(t){return $r(t[0]-1)&&$r(t[1])&&$r(t[2])&&$r(t[3]-1)}function mc(t,e,r){var i,n,o;!e||$r((o=e)[4])&&$r(o[5])&&_c(e)||(i=r?10:1e4,t.transform=_c(e)?"translate("+dc(e[4]*i)/i+" "+dc(e[5]*i)/i+")":"matrix("+Jr((n=e)[0])+","+Jr(n[1])+","+Jr(n[2])+","+Jr(n[3])+","+ti(n[4])+","+ti(n[5])+")")}function xc(t,e,r){for(var i=t.points,n=[],o=0;o<i.length;o++)n.push(dc(i[o][0]*r)/r),n.push(dc(i[o][1]*r)/r);e.points=n.join(" ")}function wc(t){return!t.smooth}var bc,Sc={circle:[(bc=L(["cx","cy","r"],function(t){return"string"==typeof t?[t,t]:t}),function(t,e,r){for(var i=0;i<bc.length;i++){var n=bc[i],o=t[n[0]];null!=o&&(e[n[1]]=dc(o*r)/r)}})],polyline:[xc,wc],polygon:[xc,wc]};function kc(t,e){var r,i,n,o,a,s,h=t.style,l=t.shape,u=Sc[t.type],c={},p=e.animation,f="path",d=t.style.strokePercent,y=e.compress&&hi(t)||4;return!u||e.willUpdate||u[1]&&!u[1](l)||p&&function(t){for(var e=t.animators,r=0;r<e.length;r++)if("shape"===e[r].targetName)return 1}(t)||d<1?(r=!t.path||t.shapeChanged(),t.path||t.createPathProxy(),i=t.path,r&&(i.beginPath(),t.buildPath(i,t.shape),t.pathUpdated()),n=i.getVersion(),a=(o=t).__svgPathBuilder,o.__svgPathVersion===n&&a&&d===o.__svgPathStrokePercent||((a=a||(o.__svgPathBuilder=new Vu)).reset(y),i.rebuildPath(a,d),a.generateStr(),o.__svgPathVersion=n,o.__svgPathStrokePercent=d),c.d=a.getStr()):(f=t.type,s=Math.pow(10,y),u[0](l,c,s)),mc(c,t.transform),vc(c,h,t,e),e.animation&&fc(t,c,e),nc(f,t.id+"",c)}function Tc(t,e){return t instanceof Zo?kc(t,e):t instanceof Sa?function(t,e){var r=t.style,i=r.image;if(i&&!H(i)&&(yc(i)?i=i.src:gc(i)&&(i=i.toDataURL())),i){var n=r.x||0,o=r.y||0,a={href:i,width:r.width,height:r.height};return n&&(a.x=n),o&&(a.y=o),mc(a,t.transform),vc(a,r,t,e),e.animation&&fc(t,a,e),nc("image",t.id+"",a)}}(t,e):t instanceof cs?function(t,e){var r=t.style,i=r.text;if(null!=i&&(i+=""),i&&!isNaN(r.x)&&!isNaN(r.y)){var n,o,a,s=r.font||E,h=r.x||0,l=(n=r.y||0,o=vn(s),"top"===(a=r.textBaseline)?n+=o/2:"bottom"===a&&(n-=o/2),n),u={"dominant-baseline":"central","text-anchor":ei[r.textAlign]||r.textAlign};if(Jh(r)){var c="",p=r.fontStyle,f=Qh(r.fontSize);if(!parseFloat(f))return;var d=r.fontFamily||g,y=r.fontWeight;c+="font-size:"+f+";font-family:"+d+";",p&&"normal"!==p&&(c+="font-style:"+p+";"),y&&"normal"!==y&&(c+="font-weight:"+y+";"),u.style=c}else u.style="font: "+s;return i.match(/\s/)&&(u["xml:space"]="preserve"),h&&(u.x=h),l&&(u.y=l),mc(u,t.transform),vc(u,r,t,e),e.animation&&fc(t,u,e),nc("text",t.id+"",u,void 0,i)}}(t,e):void 0}function Cc(t,e,r,i){var n,o=t[r],a={gradientUnits:o.global?"userSpaceOnUse":"objectBoundingBox"};if(ni(o))n="linearGradient",a.x1=o.x,a.y1=o.y,a.x2=o.x2,a.y2=o.y2;else{if(!oi(o))return;n="radialGradient",a.cx=Z(o.x,.5),a.cy=Z(o.y,.5),a.r=Z(o.r,.5)}for(var s=o.colorStops,h=[],l=0,u=s.length;l<u;++l){var c=100*ti(s[l].offset)+"%",p=Kr(s[l].color),f=p.color,d=p.opacity,y={offset:c};y["stop-color"]=f,d<1&&(y["stop-opacity"]=d),h.push(nc("stop",l+"",y))}var g=oc(nc(n,"",a,h)),v=i.gradientCache,_=v[g];_||(_=i.zrId+"-g"+i.gradientIdx++,v[g]=_,a.id=_,i.defs[_]=nc(n,_,a,h)),e[r]=si(_)}function Pc(t,e,r,i){var n,o,a,s,h,l,u,c,p,f,d,y,g,v,_,m=t.style[r],x=t.getBoundingRect(),w={},b=m.repeat,S="no-repeat"===b,k="repeat-x"===b,T="repeat-y"===b;ri(m)?(o=m.imageWidth,a=m.imageHeight,s=void 0,H(h=m.image)?s=h:yc(h)?s=h.src:gc(h)&&(s=h.toDataURL()),"undefined"==typeof Image?(J(o,l="Image width/height must been given explictly in svg-ssr renderer."),J(a,l)):null!=o&&null!=a||(u=function(t,e){var r,i,n;t&&(r=t.elm,i=o||e.width,n=a||e.height,"pattern"===t.tag&&(k?(n=1,i/=x.width):T&&(i=1,n/=x.height)),t.attrs.width=i,t.attrs.height=n,r&&(r.setAttribute("width",i),r.setAttribute("height",n)))},(c=Lh(s,null,t,function(t){S||u(y,t),u(n,t)}))&&c.width&&c.height&&(o=o||c.width,a=a||c.height)),n=nc("image","img",{href:s,width:o,height:a}),w.width=o,w.height=a):m.svgElement&&(n=C(m.svgElement),w.width=m.svgWidth,w.height=m.svgHeight),n&&(S?p=f=1:k?(f=1,p=w.width/x.width):T?(p=1,f=w.height/x.height):w.patternUnits="userSpaceOnUse",null==p||isNaN(p)||(w.width=p),null==f||isNaN(f)||(w.height=f),(d=li(m))&&(w.patternTransform=d),g=oc(y=nc("pattern","",w,[n])),(_=(v=i.patternCache)[g])||(_=i.zrId+"-p"+i.patternIdx++,v[g]=_,w.id=_,y=i.defs[_]=nc("pattern",_,w,[n])),e[r]=si(_))}function Mc(t){return document.createTextNode(t)}function Ac(t,e,r){t.insertBefore(e,r)}function Lc(t,e){t.removeChild(e)}function Dc(t,e){t.appendChild(e)}function zc(t){return t.parentNode}function Ic(t){return t.nextSibling}function Oc(t,e){t.textContent=e}var Rc=58,Fc=120,Bc=nc("","");function Hc(t){return void 0===t}function Nc(t){return void 0!==t}function Ec(t,e){var r=t.key===e.key;return t.tag===e.tag&&r}function Wc(t){var e,r=t.children,i=t.tag;if(Nc(i)){var n=t.elm=ic(i);if(Yc(Bc,t),G(r))for(e=0;e<r.length;++e){var o=r[e];null!=o&&Dc(n,Wc(o))}else Nc(t.text)&&!W(t.text)&&Dc(n,Mc(t.text))}else t.elm=Mc(t.text);return t.elm}function Xc(t,e,r,i,n){for(;i<=n;++i){var o=r[i];null!=o&&Ac(t,Wc(o),e)}}function qc(t,e,r,i){for(;r<=i;++r){var n=e[r];null!=n&&(Nc(n.tag)?Lc(zc(n.elm),n.elm):Lc(t,n.elm))}}function Yc(t,e){var r,i=e.elm,n=t&&t.attrs||{},o=e.attrs||{};if(n!==o){for(r in o){var a=o[r];n[r]!==a&&(!0===a?i.setAttribute(r,""):!1===a?i.removeAttribute(r):r.charCodeAt(0)!==Fc?i.setAttribute(r,a):"xmlns:xlink"===r||"xmlns"===r?i.setAttributeNS(ec,r,a):r.charCodeAt(3)===Rc?i.setAttributeNS(rc,r,a):r.charCodeAt(5)===Rc?i.setAttributeNS(tc,r,a):i.setAttribute(r,a))}for(r in n)r in o||i.removeAttribute(r)}}function jc(t,e,r){for(var i,n,o,a=0,s=0,h=e.length-1,l=e[0],u=e[h],c=r.length-1,p=r[0],f=r[c];a<=h&&s<=c;)null==l?l=e[++a]:null==u?u=e[--h]:null==p?p=r[++s]:null==f?f=r[--c]:Ec(l,p)?(Vc(l,p),l=e[++a],p=r[++s]):Ec(u,f)?(Vc(u,f),u=e[--h],f=r[--c]):Ec(l,f)?(Vc(l,f),Ac(t,l.elm,Ic(u.elm)),l=e[++a],f=r[--c]):p=(Ec(u,p)?(Vc(u,p),Ac(t,u.elm,l.elm),u=e[--h]):(Hc(i)&&(i=function(t,e,r){for(var i={},n=e;n<=r;++n){var o=t[n].key;void 0!==o&&(i[o]=n)}return i}(e,a,h)),Hc(n=i[p.key])||(o=e[n]).tag!==p.tag?Ac(t,Wc(p),l.elm):(Vc(o,p),e[n]=void 0,Ac(t,o.elm,l.elm))),r[++s]);(a<=h||s<=c)&&(h<a?Xc(t,null==r[c+1]?null:r[c+1].elm,r,s,c):qc(t,e,a,h))}function Vc(t,e){var r=e.elm=t.elm,i=t.children,n=e.children;t!==e&&(Yc(t,e),Hc(e.text)?Nc(i)&&Nc(n)?i!==n&&jc(r,i,n):Nc(n)?(Nc(t.text)&&Oc(r,""),Xc(r,null,n,0,n.length-1)):Nc(i)?qc(r,i,0,i.length-1):Nc(t.text)&&Oc(r,""):t.text!==e.text&&(Nc(i)&&qc(r,i,0,i.length-1),Oc(r,e.text)))}var Uc=0,Gc=(Zc.prototype.getType=function(){return this.type},Zc.prototype.getViewportRoot=function(){return this._viewport},Zc.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},Zc.prototype.getSvgDom=function(){return this._svgDom},Zc.prototype.refresh=function(){var t,e,r,i,n;this.root&&((t=this.renderToVNode({willUpdate:!0})).attrs.style="position:absolute;left:0;top:0;user-select:none",Ec(e=this._oldVNode,r=t)?Vc(e,r):(n=zc(i=e.elm),Wc(r),null!==n&&(Ac(n,r.elm,Ic(i)),qc(n,[e],0,0))),this._oldVNode=t)},Zc.prototype.renderOneToVNode=function(t){return Tc(t,ac(this._id))},Zc.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),r=this._width,i=this._height,n=ac(this._id);n.animation=t.animation,n.willUpdate=t.willUpdate,n.compress=t.compress;var o=[],a=this._bgVNode=function(t,e,r,i){var n,o,a,s;return r&&"none"!==r&&(n=nc("rect","bg",{width:t,height:e,x:"0",y:"0",id:"0"}),ai(r)?Cc({fill:r},n.attrs,"fill",i):ii(r)?Pc({style:{fill:r},dirty:ct,getBoundingRect:function(){return{width:t,height:e}}},n.attrs,"fill",i):(o=Kr(r),a=o.color,s=o.opacity,n.attrs.fill=a,s<1&&(n.attrs["fill-opacity"]=s))),n}(r,i,this._backgroundColor,n);a&&o.push(a);var s=t.compress?null:this._mainVNode=nc("g","main",{},[]);this._paintList(e,n,s?s.children:o),s&&o.push(s);var h,l,u,c,p,f,d,y,g,v=L(F(n.defs),function(t){return n.defs[t]});return v.length&&o.push(nc("defs","defs",{},v)),t.animation&&(u=n.cssNodes,c=n.cssAnims,f=" {"+(p="\n"),d=p+"}",y=L(F(u),function(e){return e+f+L(F(u[e]),function(t){return t+":"+u[e][t]+";"}).join(p)+d}).join(p),g=L(F(c),function(i){return"@keyframes "+i+f+L(F(c[i]),function(r){return r+f+L(F(c[i][r]),function(t){var e=c[i][r][t];return"d"===t&&(e='path("'+e+'")'),t+":"+e+";"}).join(p)+d}).join(p)+d}).join(p),(h=y||g?["<![CDATA[",y,g,"]]>"].join(p):"")&&(l=nc("style","stl",{},[],h),o.push(l))),sc(r,i,o,t.useViewBox)},Zc.prototype.renderToString=function(t){return t=t||{},oc(this.renderToVNode({animation:Z(t.cssAnimation,!0),willUpdate:!1,compress:!0,useViewBox:Z(t.useViewBox,!0)}),{newline:!0})},Zc.prototype.setBackgroundColor=function(t){this._backgroundColor=t},Zc.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},Zc.prototype._paintList=function(t,e,r){for(var i,n,o,a,s,h,l,u,c,p=t.length,f=[],d=0,y=0,g=0;g<p;g++){var v=t[g];if(!v.invisible){for(var _=v.__clipPaths,m=_&&_.length||0,x=n&&n.length||0,w=void 0,w=Math.max(m-1,x-1);0<=w&&(!_||!n||_[w]!==n[w]);w--);for(var b=x-1;w<b;b--)i=f[--d-1];for(var S=w+1;S<m;S++){var k={};o=_[S],a=k,c=u=l=h=void 0,l=(s=e).clipPathCache,u=s.defs,(c=l[o.id])||(h={id:c=s.zrId+"-c"+s.clipPathIdx++},u[l[o.id]=c]=nc("clipPath",c,h,[kc(o,s)])),a["clip-path"]=si(c);var T=nc("g","clip-g-"+y++,k,[]);(i?i.children:r).push(T),i=f[d++]=T}n=_;var C=Tc(v,e);C&&(i?i.children:r).push(C)}}},Zc.prototype.resize=function(t,e){var r,i,n,o=this._opts,a=this.root,s=this._viewport;null!=t&&(o.width=t),null!=e&&(o.height=e),a&&s&&(s.style.display="none",t=fu(a,0,o),e=fu(a,1,o),s.style.display=""),this._width===t&&this._height===e||(this._width=t,this._height=e,s&&((r=s.style).width=t+"px",r.height=e+"px"),ii(this._backgroundColor)?this.refresh():((i=this._svgDom)&&(i.setAttribute("width",t),i.setAttribute("height",e)),(n=this._bgVNode&&this._bgVNode.elm)&&(n.setAttribute("width",t),n.setAttribute("height",e))))},Zc.prototype.getWidth=function(){return this._width},Zc.prototype.getHeight=function(){return this._height},Zc.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},Zc.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},Zc.prototype.toDataURL=function(t){var e=this.renderToString(),r="data:image/svg+xml;";return t?(e=ui(e))&&r+"base64,"+e:r+"charset=UTF-8,"+encodeURIComponent(e)},Zc);function Zc(t,e,r){var i,n;this.type="svg",this.refreshHover=Kc(),this.configLayer=Kc(),this.storage=e,this._opts=r=I({},r),this.root=t,this._id="zr"+Uc++,this._oldVNode=sc(r.width,r.height),t&&!r.ssr&&((i=this._viewport=document.createElement("div")).style.cssText="position:relative;overflow:hidden",n=this._svgDom=this._oldVNode.elm=ic("svg"),Yc(null,this._oldVNode),i.appendChild(n),t.appendChild(i)),this.resize(r.width,r.height)}function Kc(){return function(){}}Hn("canvas",Nu),Hn("svg",Gc),t.Arc=hl,t.ArcShape=sl,t.BezierCurve=dl,t.BezierCurveShape=cl,t.BoundingRect=Se,t.Circle=Pa,t.CircleShape=Ca,t.CompoundPath=Sh,t.Displayable=Vn,t.Droplet=_l,t.DropletShape=vl,t.Element=Tn,t.Ellipse=Ha,t.EllipseShape=Ba,t.Group=zn,t.Heart=bl,t.HeartShape=wl,t.Image=Sa,t.IncrementalDisplayable=Ph,t.Isogon=Al,t.IsogonShape=Ml,t.Line=qa,t.LineShape=Xa,t.LinearGradient=is,t.OrientedBoundingRect=au,t.Path=Zo,t.Pattern=eu,t.Point=fe,t.Polygon=Ga,t.PolygonShape=Ua,t.Polyline=$a,t.PolylineShape=Qa,t.RadialGradient=as,t.Rect=Oa,t.RectShape=za,t.Ring=Il,t.RingShape=zl,t.Rose=El,t.RoseShape=Nl,t.Sector=Gs,t.SectorShape=Us,t.Star=Ul,t.StarShape=Vl,t.TSpan=cs,t.Text=Vh,t.Trochoid=Jl,t.TrochoidShape=$l,t.color=Gr,t.dispose=function(t){t.dispose()},t.disposeAll=function(){for(var t in Rn)Rn.hasOwnProperty(t)&&Rn[t].dispose();Rn={}},t.getInstance=function(t){return Rn[t]},t.init=function(t,e){var r=new Fn(w(),t,e);return Rn[r.id]=r},t.matrix=pe,t.morph=bh,t.parseSVG=function(t,e){return(new vs).parse(t,e)},t.path=ma,t.registerPainter=Hn,t.setPlatformAPI=function(t){for(var e in f)t[e]&&(f[e]=t[e])},t.showDebugDirtyRect=function(t,n){n=n||{};var e=t.painter;if(!e.getLayers)throw new Error("Debug dirty rect can only been used on canvas renderer.");if(e.isSingleCanvas())throw new Error("Debug dirty rect can only been used on zrender inited with container.");var o=document.createElement("div");o.style.cssText="\nposition:absolute;\nleft:0;\ntop:0;\nright:0;\nbottom:0;\npointer-events:none;\n",o.className="ec-debug-dirty-rect-container";var a=[],r=t.dom;r.appendChild(o),"static"===getComputedStyle(r).position&&(r.style.position="relative"),t.on("rendered",function(){if(e.getLayers){var i=0;e.eachBuiltinLayer(function(t){if(t.debugGetPaintRects)for(var e=t.debugGetPaintRects(),r=0;r<e.length;r++)e[r].width&&e[r].height&&(a[i]||(a[i]=new hu(n.style),o.appendChild(a[i].dom)),a[i].show(n.autoHideDelay),a[i].update(e[r]),i++)});for(var t=i;t<a.length;t++)a[t].hide()}})},t.util=ft,t.vector=zt,t.version="5.4.1",Object.defineProperty(t,"__esModule",{value:!0})});