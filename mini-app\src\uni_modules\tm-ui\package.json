{"id": "tm-ui", "displayName": "tmui3.2.0 vue3 ts Ui组件库 ", "version": "3.2.07", "description": "继承了tmui4.0x的设计", "keywords": ["ui", "vue3", "typeScript", ""], "repository": "https://gitee.com/LYTB/tmui-design", "engines": {"HBuilderX": "^3.6.18", "uni-app": "^4.06", "uni-app-x": ""}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/tmui-cli", "type": "component-vue", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "x", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "x", "android": "√", "ios": "√", "harmony": "-"}, "mp": {"weixin": "√", "alipay": "-", "toutiao": "-", "baidu": "-", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}}