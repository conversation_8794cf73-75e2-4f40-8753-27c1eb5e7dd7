import { use } from 'echarts/lib/extension.js';
export * from 'echarts/lib/export/core.js';
import { install as CanvasRenderer } from 'echarts/lib/renderer/installCanvasRenderer.js';
import { install as Line<PERSON><PERSON> } from 'echarts/lib/chart/line/install.js';
import { install as <PERSON><PERSON><PERSON> } from 'echarts/lib/chart/bar/install.js';
import { install as Pie<PERSON><PERSON> } from 'echarts/lib/chart/pie/install.js';
import { install as GridSimpleComponent } from 'echarts/lib/component/grid/installSimple.js';
import { install as AriaComponent } from 'echarts/lib/component/aria/install.js';
import { install as DatasetComponent } from 'echarts/lib/component/dataset/install.js';
import { install as Tooltip } from 'echarts/lib/component/tooltip/install.js';

use([CanvasRenderer]);
use([<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]);
use([GridSimpleComponent, AriaComponent, DatasetComponent,Tooltip]);