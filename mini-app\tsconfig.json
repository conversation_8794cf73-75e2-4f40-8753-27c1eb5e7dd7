{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "types": ["@dcloudio/types"], "paths": {"@/*": ["./src/*"]}}, "files": ["./components.d.ts"], "include": ["./src/*.vue", "./src/**/*.vue", "./src/uni_modules/tm-ui/**/*.vue", "./src/uni_modules/tm-ui/components/*.vue", "./src/uni_modules/tm-ui/components/**/*.vue", "./src/uni_modules/tm-ui/**/*.ts", "./src/uni_modules/tm-ui/*.ts", "./src/uni_modules/tm-ui/*.d.ts", "./src/uni_modules/tm-ui/**/*.d.ts", "./src/*.ts", "./src/*.d.ts", "./src/**/*.ts"]}