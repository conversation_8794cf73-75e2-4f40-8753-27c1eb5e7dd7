/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    MpNode: typeof import('./src/uni_modules/tm-ui/components/tm-html/mp-node.vue')['default']
    PickerItem: typeof import('./src/uni_modules/tm-ui/components/tm-picker-view/picker-item.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TmAlert: typeof import('./src/uni_modules/tm-ui/components/tm-alert/tm-alert.vue')['default']
    TmAvatarGroup: typeof import('./src/uni_modules/tm-ui/components/tm-avatar-group/tm-avatar-group.vue')['default']
    TmBadge: typeof import('./src/uni_modules/tm-ui/components/tm-badge/tm-badge.vue')['default']
    TmBetweenTime: typeof import('./src/uni_modules/tm-ui/components/tm-between-time/tm-between-time.vue')['default']
    TmButton: typeof import('./src/uni_modules/tm-ui/components/tm-button/tm-button.vue')['default']
    TmCell: typeof import('./src/uni_modules/tm-ui/components/tm-cell/tm-cell.vue')['default']
    TmCheckbox: typeof import('./src/uni_modules/tm-ui/components/tm-checkbox/tm-checkbox.vue')['default']
    TmCheckboxGroup: typeof import('./src/uni_modules/tm-ui/components/tm-checkbox-group/tm-checkbox-group.vue')['default']
    TmCodeInput: typeof import('./src/uni_modules/tm-ui/components/tm-code-input/tm-code-input.vue')['default']
    TmCol: typeof import('./src/uni_modules/tm-ui/components/tm-col/tm-col.vue')['default']
    TmCollapse: typeof import('./src/uni_modules/tm-ui/components/tm-collapse/tm-collapse.vue')['default']
    TmCollapseItem: typeof import('./src/uni_modules/tm-ui/components/tm-collapse-item/tm-collapse-item.vue')['default']
    TmCountdown: typeof import('./src/uni_modules/tm-ui/components/tm-countdown/tm-countdown.vue')['default']
    TmDateView: typeof import('./src/uni_modules/tm-ui/components/tm-date-view/tm-date-view.vue')['default']
    TmDrawer: typeof import('./src/uni_modules/tm-ui/components/tm-drawer/tm-drawer.vue')['default']
    TmEchart: typeof import('./src/uni_modules/tm-ui/components/tm-echart/tm-echart.vue')['default']
    TmForm: typeof import('./src/uni_modules/tm-ui/components/tm-form/tm-form.vue')['default']
    TmFormItem: typeof import('./src/uni_modules/tm-ui/components/tm-form-item/tm-form-item.vue')['default']
    TmHtml: typeof import('./src/uni_modules/tm-ui/components/tm-html/tm-html.vue')['default']
    TmIcon: typeof import('./src/uni_modules/tm-ui/components/tm-icon/tm-icon.vue')['default']
    TmImage: typeof import('./src/uni_modules/tm-ui/components/tm-image/tm-image.vue')['default']
    TmInput: typeof import('./src/uni_modules/tm-ui/components/tm-input/tm-input.vue')['default']
    TmModal: typeof import('./src/uni_modules/tm-ui/components/tm-modal/tm-modal.vue')['default']
    TmMoney: typeof import('./src/uni_modules/tm-ui/components/tm-money/tm-money.vue')['default']
    TmNavbar: typeof import('./src/uni_modules/tm-ui/components/tm-navbar/tm-navbar.vue')['default']
    TmPicker: typeof import('./src/uni_modules/tm-ui/components/tm-picker/tm-picker.vue')['default']
    TmPickerDate: typeof import('./src/uni_modules/tm-ui/components/tm-picker-date/tm-picker-date.vue')['default']
    TmPickerSelected: typeof import('./src/uni_modules/tm-ui/components/tm-picker-selected/tm-picker-selected.vue')['default']
    TmPickerView: typeof import('./src/uni_modules/tm-ui/components/tm-picker-view/tm-picker-view.vue')['default']
    TmRadio: typeof import('./src/uni_modules/tm-ui/components/tm-radio/tm-radio.vue')['default']
    TmRadioGroup: typeof import('./src/uni_modules/tm-ui/components/tm-radio-group/tm-radio-group.vue')['default']
    TmRate: typeof import('./src/uni_modules/tm-ui/components/tm-rate/tm-rate.vue')['default']
    TmRow: typeof import('./src/uni_modules/tm-ui/components/tm-row/tm-row.vue')['default']
    TmSheet: typeof import('./src/uni_modules/tm-ui/components/tm-sheet/tm-sheet.vue')['default']
    TmSkeleton: typeof import('./src/uni_modules/tm-ui/components/tm-skeleton/tm-skeleton.vue')['default']
    TmSku: typeof import('./src/uni_modules/tm-ui/components/tm-sku/tm-sku.vue')['default']
    TmSliderMenu: typeof import('./src/uni_modules/tm-ui/components/tm-slider-menu/tm-slider-menu.vue')['default']
    TmStepper: typeof import('./src/uni_modules/tm-ui/components/tm-stepper/tm-stepper.vue')['default']
    TmSticky: typeof import('./src/uni_modules/tm-ui/components/tm-sticky/tm-sticky.vue')['default']
    TmStickyHeader: typeof import('./src/uni_modules/tm-ui/components/tm-sticky-header/tm-sticky-header.vue')['default']
    TmSwitch: typeof import('./src/uni_modules/tm-ui/components/tm-switch/tm-switch.vue')['default']
    TmTabbar: typeof import('./src/uni_modules/tm-ui/components/tm-tabbar/tm-tabbar.vue')['default']
    TmTabs: typeof import('./src/uni_modules/tm-ui/components/tm-tabs/tm-tabs.vue')['default']
    TmTabsItem: typeof import('./src/uni_modules/tm-ui/components/tm-tabs-item/tm-tabs-item.vue')['default']
    TmTag: typeof import('./src/uni_modules/tm-ui/components/tm-tag/tm-tag.vue')['default']
    TmText: typeof import('./src/uni_modules/tm-ui/components/tm-text/tm-text.vue')['default']
    TmUploadPhoto: typeof import('./src/uni_modules/tm-ui/components/tm-upload-photo/tm-upload-photo.vue')['default']
    TmUploadVideo: typeof import('./src/uni_modules/tm-ui/components/tm-upload-video/tm-upload-video.vue')['default']
  }
}
