{"name": "tmui3.2", "appid": "__UNI__F097BC1", "description": "tmui3.0新一代组件库，拥抱vue3,pinia,tmui3.0 demo展示应用", "versionName": "3.2.0", "versionCode": 320, "transformPx": false, "app-plus": {"darkmode": true, "themeLocation": "tmui/theme.json", "usingComponents": true, "ignoreVersion": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "safearea": {"bottom": {"offset": "none"}}, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Canvas": "nvue canvas", "VideoPlayer": {}, "Camera": {}, "Barcode": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "minSdkVersion": 21, "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"idfa": false, "dSYMs": false}, "sdkConfigs": {"ad": {}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "splashscreen": {"useOriginalMsgbox": true}}, "uniStatistics": {"enable": false}}, "quickapp": {}, "mp-weixin": {"lazyCodeLoading": "requiredComponents", "mergeVirtualHostAttributes": true, "darkmode": false, "appid": "wxa97ca08b1ed46b0a", "setting": {"urlCheck": false, "es6": true, "minified": true}, "usingComponents": true, "uniStatistics": {"enable": false}}, "mp-alipay": {"usingComponents": true, "uniStatistics": {"enable": false}, "appid": "2019042364306232"}, "mp-baidu": {"usingComponents": true, "uniStatistics": {"enable": false}}, "mp-toutiao": {"usingProvide": true, "usingComponents": true, "uniStatistics": {"enable": false}, "setting": {"es6": true, "minified": true, "urlCheck": false}}, "uniStatistics": {"enable": false, "version": "2"}, "vueVersion": "3", "h5": {"darkmode": false, "themeLocation": "tmui/theme.json", "uniStatistics": {"enable": false}, "title": "tmui3.0", "router": {"mode": "hash", "base": "/h5/"}, "optimization": {"treeShaking": {"enable": true}}, "devServer": {"https": false}}, "mp-jd": {"uniStatistics": {"enable": false}}, "mp-kuaishou": {"uniStatistics": {"enable": false}}, "mp-lark": {"uniStatistics": {"enable": false}}, "mp-qq": {"uniStatistics": {"enable": false}, "setting": {"es6": true, "minified": true, "urlCheck": false}, "appid": "1112154209"}, "quickapp-webview-huawei": {"uniStatistics": {"enable": false}}, "quickapp-webview-union": {"uniStatistics": {"enable": false}}}