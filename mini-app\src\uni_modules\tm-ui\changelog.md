## 3.2.07（2025-07-16）
* tm-stepperx组件修复可能在深层嵌套时，可能默认值时可能会有错误。
* 表单校验，兼容更广泛的可能类型不兼容时的处理情况。并增加了些属性，更新了校验时机的处理（已在非常复杂的表单嵌套及主/子表单动态渲染结构中得到验证）
* tmDate及picker-date,date-view,picker-between修复了某些问题。【请务必升级】
* tm-stepper增加autoHideBtn属性及示例，自动隐藏减号，同时增加input-style属性
* 完善组件针对style属性的ts提示，这样可在写对象css时有css全局字典提示，并解决爆红问题，比如tmText属性:_style="{fontSize:16}",时会有css提示。
* 修复tm-html和navbar组件，如果页面被分享到朋友圈，当用户在朋友圈浏览时会出现错误（注意不是打开仅是朋友圈的内嵌浏览）
* 修复tm-upload-photo，tm-upload-videou状态不变化，成功后，数据不同步。
## 3.2.06（2025-04-15）
* picker-date复制错了
## 3.2.05（2025-04-14）
* 修复了一些问题
* tmNavbar属性删除了scrollTop属性,改为内部实现。
* 修复弹层类modal,drawer类可能在不同平台上的显示的问题。
* 修改了input组件样式
* 增加了tmAvatarGroup头像组件
* 增加了tmSkeleton加载动画组件
* 修复tm-tabs如果值为空与0时可能无法判断
* 修复uploadphoto,uploadvideo可能外部无法清空,可能外部造成内部多次上传,可能内外数据污染.
* 给各个弹层添加了禁用disabled属性
* tm-echart添加了点击事件，兼容了pc操作
* tmDate,tmPickerData,tm-between-time现支持非正常格式了如：YYYY,YYYY-MM,YYYY-MM-DD,YYYY-MM-DD HH,YYYY-MM-DD HH:mm,YYYY-MM-DD HH:mm:ss
同时提供了属性：formatSyncValue用于格式化同步到vmodel中，默认是不同步的，默认它是返回一个标准的时间戳字符串，这样就不需要vmodel:modal-str来绑定，可以直接提交为你需要的字段格式值。
## 3.2.04（2024-12-24）
* 更新优化,修复bug和新增部分组件,
## 3.2.03（2024-11-21）
* 更新和新增部分组件,
## 3.2.02（2024-11-12）
* 更新部分组件,将会提供设计专用的可视化超级编辑器，适用于有设计经验，远程案例预览，模板保存，多面板设计，多选项同时修改样式，类似即时，xd,sketch等设计软件，敬请期待
## 3.2.01（2024-10-28）
* 更新部分组件
## 3.2.0（2024-10-11）
* [注意]本版本不向下兼容,可以直接通过市场导入或者在插件目录右键升级到项目中
* [3.2.x文档链接](https://tmui.design/)
* 1. 本版本不向下兼容!!!!!
* 2. 不再兼容nvue平台,请谨慎升级
* 3. 本版本不是之前的3.1.x版本的升级版本,无法平移过度到本版本
* 4. 本版本为全新的tmui4.0x版本的小程序版本,风格,标准,细节与其一样,属性也大多相同,但内部是本质的不同.不可以4.0x混用
* 5. 本版本以webview app + 小程序为准兼容,并且不提供任何原生插件,如果需要原生请使用tmui4.0x来支持我.
* 6. 本版本一样是免费的,可以自由的使用,商用,任意修改不用担心版权问题.但请保留版权信息.但不得发布为公共的竞品版本,发现立即追究版权.
* 7. 本版本更新迭代比较慢,力求后续兼容鸿蒙系统及所有小程序,因此会逐步减少组件数量,但会保证组件质量,保留最基本的组件库功能.
* 8. 如果需要赞助或者捐赠,可以通过官网付款码赞助支持我的开发或者购买tmui4.0x以支持.
* 发布第一个3.2.0版本为公测版本
* 如果有遇到问题请提交bug至[Gitee](https://gitee.com/LYTB/tm-vuetify-for-vue3)
